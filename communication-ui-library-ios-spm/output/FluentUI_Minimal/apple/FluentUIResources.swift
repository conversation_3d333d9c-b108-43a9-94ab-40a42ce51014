//
//  FluentUIResources.swift
//  FluentUI Resources Minimal Swift Module Interface
//
//  This provides minimal Swift module interface for FluentUIResources.
//  Actual resources come from embedded FluentUI in Azure XCFrameworks.
//

import Foundation

// Minimal Swift module interface - no resources
// This satisfies FluentUI dependency requirements without providing competing resources

@objc public class FluentUIResourcesModule: NSObject {
    // Empty stub class to satisfy module requirements
    // All actual FluentUI resources come from embedded version in Azure XCFrameworks
}
