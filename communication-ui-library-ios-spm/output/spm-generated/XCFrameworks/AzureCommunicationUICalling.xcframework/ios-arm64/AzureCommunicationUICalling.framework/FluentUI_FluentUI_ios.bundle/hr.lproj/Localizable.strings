//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "U tijeku";
"Accessibility.ActivityIndicator.Stopped.label" = "Zaustavljen napredak";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Upozorenje";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Odbaci";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dvaput dodirnite da biste odbacili";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Gotovo";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dvaput dodirnite za uključivanje/isključivanje odabira ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalendar";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "<PERSON>da<PERSON> datuma";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "Broj događaja: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "nema događaja";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mjesec";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dan";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Godina";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Datum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Sat";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuta";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Tjedan u mjesecu";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dan u tjednu";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "1.";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "2.";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "3.";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "4.";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Zadnji";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Proširi";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dvaput dodirnite da biste proširili";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Sažmi";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dvaput dodirnite za sažimanje";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Gotovo";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Nije uspjelo";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Učitavanje";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d od %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil računa";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d od %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dvaput dodirnite da biste pogledali dodatne akcije";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Dodatne akcije";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dvaput dodirnite da biste uključili ili isključili postavku";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Uključeno";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Isključeno";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ stavke";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, nepročitano";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Više";

/* Generic label for cancel action */
"Common.Cancel" = "Odustani";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Zajednički se koristi";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Samo ja";

/* Just now date string */
"Date.Now" = "Upravo";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Prije %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Prije %ldh";

/* Yesterday string */
"Date.Yesterday" = "Jučer";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Jučer u %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ u %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Pretraživanje direktorija";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Vrijeme početka";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Vrijeme završetka";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Datum početka";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Datum završetka";

/* Presence - Available status */
"Presence.Available" = "Dostupno";

/* Presence - Away status */
"Presence.Away" = "Nisam tu";

/* Presence - Busy status */
"Presence.Busy" = "Zauzet";

/* Presence - Do not disturb status */
"Presence.DND" = "Ne ometaj";

/* Presence - Out of office status */
"Presence.OOF" = "Nisam u uredu";

/* Presence - Offline status */
"Presence.Offline" = "Izvan mreže";

/* Presence - Unknown status */
"Presence.Unknown" = "Nepoznato";

/* Presence - Blocked status */
"Presence.Blocked" = "Blokiran";
