//
//  ACSCredentialValidationTests.swift
//  iOSProjectTests
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import XCTest
@testable import iOSProject

class ACSCredentialValidationTests: XCTestCase {
    
    // MARK: - Test Cases
    
    func testCredentialValidation_ValidCredentials() {
        // Given
        let validCredentials = ACSCredentials(
            connectionString: "endpoint=https://test-resource.communication.azure.com/;accesskey=validaccesskey123",
            endpoint: "https://test-resource.communication.azure.com/",
            accessKey: "validaccesskey123",
            resourceName: "test-resource",
            environment: .production,
            isUsingMockCredentials: false
        )
        
        let configuration = ACSConfiguration(
            connectionString: validCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: validCredentials
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .valid)
    }
    
    func testCredentialValidation_MockCredentials() {
        // Given
        let mockCredentials = ACSCredentials(
            connectionString: "endpoint=https://test-resource.communication.azure.com/;accesskey=validaccesskey123",
            endpoint: "https://test-resource.communication.azure.com/",
            accessKey: "validaccesskey123",
            resourceName: "test-resource",
            environment: .development,
            isUsingMockCredentials: true
        )
        
        let configuration = ACSConfiguration(
            connectionString: mockCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: mockCredentials
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.usingMockCredentials))
    }
    
    func testCredentialValidation_PlaceholderCredentials() {
        // Given
        let placeholderCredentials = ACSCredentials(
            connectionString: "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key",
            endpoint: "https://your-acs-resource.communication.azure.com/",
            accessKey: "your-access-key",
            resourceName: "your-acs-resource",
            environment: .development,
            isUsingMockCredentials: false
        )
        
        let configuration = ACSConfiguration(
            connectionString: placeholderCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: placeholderCredentials
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.placeholderCredentials))
    }
    
    func testCredentialValidation_MissingCredentials() {
        // Given
        let configuration = ACSConfiguration(
            connectionString: "endpoint=https://test-resource.communication.azure.com/;accesskey=validkey",
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: nil
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.missingCredentials))
    }
    
    func testCredentialValidation_InvalidConnectionString() {
        // Given
        let invalidCredentials = ACSCredentials(
            connectionString: "invalid-connection-string",
            endpoint: "https://test-resource.communication.azure.com/",
            accessKey: "validaccesskey123",
            resourceName: "test-resource",
            environment: .production,
            isUsingMockCredentials: false
        )
        
        let configuration = ACSConfiguration(
            connectionString: invalidCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: invalidCredentials
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.invalidConnectionString))
    }
    
    func testCredentialValidation_EmptyConnectionString() {
        // Given
        let emptyCredentials = ACSCredentials(
            connectionString: "",
            endpoint: "https://test-resource.communication.azure.com/",
            accessKey: "validaccesskey123",
            resourceName: "test-resource",
            environment: .production,
            isUsingMockCredentials: false
        )
        
        let configuration = ACSConfiguration(
            connectionString: emptyCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: emptyCredentials
        )
        
        // When
        let result = configuration.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.invalidConnectionString))
    }
    
    func testACSService_ValidateCredentials() {
        // Given
        let validCredentials = ACSCredentials(
            connectionString: "endpoint=https://test-resource.communication.azure.com/;accesskey=validaccesskey123",
            endpoint: "https://test-resource.communication.azure.com/",
            accessKey: "validaccesskey123",
            resourceName: "test-resource",
            environment: .production,
            isUsingMockCredentials: false
        )
        
        let configuration = ACSConfiguration(
            connectionString: validCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: validCredentials
        )
        
        let mockTokenProvider = MockACSTokenProvider()
        let acsService = ACSService(configuration: configuration, tokenProvider: mockTokenProvider)
        
        // When
        let result = acsService.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .valid)
    }
    
    func testMockACSService_ValidateCredentials() {
        // Given
        let mockCredentials = ACSCredentials(
            connectionString: "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key",
            endpoint: "https://your-acs-resource.communication.azure.com/",
            accessKey: "your-access-key",
            resourceName: "your-acs-resource",
            environment: .development,
            isUsingMockCredentials: true
        )
        
        let configuration = ACSConfiguration(
            connectionString: mockCredentials.connectionString,
            defaultDisplayName: "Test User",
            callTimeout: 30.0,
            credentials: mockCredentials
        )
        
        let mockTokenProvider = MockACSTokenProvider()
        let mockACSService = MockACSService(configuration: configuration, tokenProvider: mockTokenProvider)
        
        // When
        let result = mockACSService.validateCredentials()
        
        // Then
        XCTAssertEqual(result, .invalid(.usingMockCredentials))
    }
}
