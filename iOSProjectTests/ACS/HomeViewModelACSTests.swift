//
//  HomeViewModelACSTests.swift
//  iOSProjectTests
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import XCTest
@testable import iOSProject

// MARK: - Home ViewModel ACS Tests

@MainActor
final class HomeViewModelACSTests: XCTestCase {
    
    // MARK: - Properties
    
    var sut: HomeViewModel!
    var mockNewsUseCase: MockNewsUseCase!
    var mockACSService: MockACSService!
    var mockTokenProvider: MockACSTokenProvider!
    
    // MARK: - Setup & Teardown
    
    override func setUp() {
        super.setUp()
        
        mockTokenProvider = MockACSTokenProvider()
        mockACSService = MockACSService(
            configuration: ACSConfiguration.default,
            tokenProvider: mockTokenProvider
        )
        mockNewsUseCase = MockNewsUseCase()
        
        sut = HomeViewModel(
            newsUseCase: mockNewsUseCase,
            acsService: mockACSService
        )
    }
    
    override func tearDown() {
        sut = nil
        mockACSService = nil
        mockTokenProvider = nil
        mockNewsUseCase = nil
        super.tearDown()
    }
    
    // MARK: - ACS Communication Tests
    
    func testStartACSCommunication_Success() async {
        // Given
        mockTokenProvider.shouldFailTokenGeneration = false
        
        // When
        sut.startACSCommunication()
        
        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
        
        // Then
        XCTAssertEqual(mockACSService.callState, .connected)
        XCTAssertFalse(sut.showError)
    }
    
    func testStartACSCommunication_TokenFailure() async {
        // Given
        mockTokenProvider.shouldFailTokenGeneration = true
        
        // When
        sut.startACSCommunication()
        
        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Then
        XCTAssertTrue(sut.showError)
        XCTAssertFalse(sut.errorMessage.isEmpty)
    }
    
    func testJoinTeamsMeeting_Success() async {
        // Given
        let meetingUrl = "https://teams.microsoft.com/l/meetup-join/test"
        mockTokenProvider.shouldFailTokenGeneration = false
        
        // When
        sut.joinTeamsMeeting(url: meetingUrl)
        
        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
        
        // Then
        XCTAssertEqual(mockACSService.callState, .connected)
        XCTAssertFalse(sut.showError)
    }
    
    func testJoinTeamsMeeting_Failure() async {
        // Given
        let meetingUrl = "https://teams.microsoft.com/l/meetup-join/test"
        mockTokenProvider.shouldFailTokenGeneration = true
        
        // When
        sut.joinTeamsMeeting(url: meetingUrl)
        
        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Then
        XCTAssertTrue(sut.showError)
        XCTAssertFalse(sut.errorMessage.isEmpty)
    }
    
    // MARK: - Error Handling Tests
    
    func testErrorHandling_DisplaysCorrectMessage() async {
        // Given
        mockTokenProvider.shouldFailTokenGeneration = true
        
        // When
        sut.startACSCommunication()
        
        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        // Then
        XCTAssertTrue(sut.showError)
        XCTAssertTrue(sut.errorMessage.contains("token") || sut.errorMessage.contains("failed"))
    }
    
    // MARK: - Integration Tests
    
    func testViewModel_InitializationWithACSService() {
        // Given & When
        let viewModel = HomeViewModel(
            newsUseCase: mockNewsUseCase,
            acsService: mockACSService
        )
        
        // Then
        XCTAssertNotNil(viewModel)
        XCTAssertFalse(viewModel.showError)
        XCTAssertFalse(viewModel.isLoading)
    }
    
    func testViewModel_ACSServiceIntegration() {
        // Given
        let initialCallState = mockACSService.callState
        
        // When
        sut.startACSCommunication()
        
        // Then
        XCTAssertEqual(initialCallState, .idle)
        // The call state will change asynchronously
    }
    
    // MARK: - State Management Tests
    
    func testViewModel_ErrorStateReset() async {
        // Given
        mockTokenProvider.shouldFailTokenGeneration = true
        sut.startACSCommunication()
        
        // Wait for error to occur
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        XCTAssertTrue(sut.showError)
        
        // When - Reset error state
        mockTokenProvider.shouldFailTokenGeneration = false
        sut.startACSCommunication()
        
        // Wait for success
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds
        
        // Then
        // Error state should be cleared on successful operation
        // Note: This depends on the implementation details of error handling
    }
    
    // MARK: - Performance Tests
    
    func testACSCommunication_Performance() {
        measure {
            sut.startACSCommunication()
            // Measure the time it takes to initiate the call
        }
    }
    
    func testMultipleACSCalls_Performance() {
        measure {
            for _ in 0..<5 {
                sut.startACSCommunication()
                // Simulate multiple rapid calls
            }
        }
    }

    // MARK: - Credential Validation Tests

    func testStartACSCommunication_InvalidCredentials() async {
        // Given
        mockACSService.shouldFailCredentialValidation = true

        // When
        sut.startACSCommunication()

        // Wait for validation to complete
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Then
        XCTAssertEqual(mockACSService.callState, .idle) // Should not proceed to call
        XCTAssertGreaterThan(mockNavigationService.toast.count, 0) // Should show error toast
    }

    func testJoinTeamsMeeting_InvalidCredentials() async {
        // Given
        let meetingUrl = "https://teams.microsoft.com/l/meetup-join/test"
        mockACSService.shouldFailCredentialValidation = true

        // When
        sut.joinTeamsMeeting(url: meetingUrl)

        // Wait for validation to complete
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Then
        XCTAssertEqual(mockACSService.callState, .idle) // Should not proceed to call
        XCTAssertGreaterThan(mockNavigationService.toast.count, 0) // Should show error toast
    }

    func testStartACSCommunication_ValidCredentials() async {
        // Given
        mockACSService.shouldFailCredentialValidation = false
        mockTokenProvider.shouldFailTokenGeneration = false

        // When
        sut.startACSCommunication()

        // Wait for async operation to complete
        try? await Task.sleep(nanoseconds: 3_000_000_000) // 3 seconds

        // Then
        XCTAssertEqual(mockACSService.callState, .connected) // Should proceed to call
    }
}

// MARK: - Mock News Use Case for Testing

class MockNewsUseCase: NewsUseCaseProtocol {
    func getNews() async throws -> [NewsItem] {
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        return [
            NewsItem(
                id: "1",
                title: "Test News",
                content: "Test content",
                imageUrl: nil,
                publishedAt: Date(),
                category: "Test"
            )
        ]
    }
    
    func getNewsItem(id: String) async throws -> NewsItem {
        try await Task.sleep(nanoseconds: 100_000_000) // 0.1 seconds
        return NewsItem(
            id: id,
            title: "Test News Item",
            content: "Test content",
            imageUrl: nil,
            publishedAt: Date(),
            category: "Test"
        )
    }
    
    func refreshNews() async throws -> [NewsItem] {
        return try await getNews()
    }
}
