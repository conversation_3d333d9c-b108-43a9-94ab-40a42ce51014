//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Σε εξέλιξη";
"Accessibility.ActivityIndicator.Stopped.label" = "Η πρόοδος διεκόπη";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Ειδοποίηση";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Κλείσιμο";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Πατήστε δύο φορές για κλείσιμο";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Τέλος";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Πατήστε δύο φορές για εναλλαγή της επιλογής";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Ημερολόγιο";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Επιλογή ημερομηνίας";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d συμβάντα";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "κανένα συμβάν";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Μήνας";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Ημέρα";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Έτος";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Ημερομηνία";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Ώρα";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Λεπτό";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "π.μ./μ.μ.";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Εβδομάδα του μήνα";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Ημέρα της εβδομάδας";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Πρώτη";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Δεύτερη";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Τρίτη";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Τέταρτη";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Τελευταία";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Ανάπτυξη";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Πατήστε δύο φορές για ανάπτυξη";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Σύμπτυξη";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Πατήστε δύο φορές για σύμπτυξη";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Τέλος";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Απέτυχε";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Φόρτωση";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d από %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Δημιουργία προφίλ λογαριασμού";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d από %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Πατήστε δύο φορές για να προβάλετε περισσότερες ενέργειες";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Περισσότερες ενέργειες";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Πατήστε δύο φορές για εναλλαγή της ρύθμισης";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Ενεργό";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Ανενεργό";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ στοιχεία";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, μη αναγνωσμένο";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Περισσότερα";

/* Generic label for cancel action */
"Common.Cancel" = "Άκυρο";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Κοινόχρηστο";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Μόνο εγώ";

/* Just now date string */
"Date.Now" = "Μόλις τώρα";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Πριν από %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Πριν από %ldh";

/* Yesterday string */
"Date.Yesterday" = "Χθες";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Χθες στις %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ στις %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Αναζήτηση στον κατάλογο";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Ώρα έναρξης";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Ώρα λήξης";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Ημερομηνία έναρξης";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Ημερομηνία λήξης";

/* Presence - Available status */
"Presence.Available" = "Διαθέσιμος/η";

/* Presence - Away status */
"Presence.Away" = "Δεν βρίσκομαι στον υπολογιστή";

/* Presence - Busy status */
"Presence.Busy" = "Απασχολημένος/η";

/* Presence - Do not disturb status */
"Presence.DND" = "Μην ενοχλείτε";

/* Presence - Out of office status */
"Presence.OOF" = "Εκτός γραφείου";

/* Presence - Offline status */
"Presence.Offline" = "Εκτός σύνδεσης";

/* Presence - Unknown status */
"Presence.Unknown" = "Άγνωστο";

/* Presence - Blocked status */
"Presence.Blocked" = "Αποκλείστηκε";
