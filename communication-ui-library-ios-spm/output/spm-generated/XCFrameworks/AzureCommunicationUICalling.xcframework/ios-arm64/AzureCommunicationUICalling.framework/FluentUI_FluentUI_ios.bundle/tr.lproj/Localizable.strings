//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Devam ediyor";
"Accessibility.ActivityIndicator.Stopped.label" = "İlerleme durduruldu";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Uyarı";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Kapat";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Kapatmak için iki kez dokunun";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Bitti";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Seçimi değiştirmek için iki kez dokunun ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Takvim";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "<PERSON><PERSON><PERSON> seçin";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d etkinlik";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "etkinlik yok";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Ay";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Gün";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Yıl";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Tarih";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Saat";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Dakika";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "ÖÖ/ÖS";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Ayın haftası";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Haftanın günü";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "İlk";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "İkinci";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Üçüncü";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Dördüncü";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Son";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Genişlet";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Genişletmek için iki kez dokunun";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Daralt";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Daraltmak için iki kez dokunun";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Bitti";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Başarısız";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Yükleniyor";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d / %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Hesap Profili";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d / %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Diğer eylemleri görüntülemek için iki kez dokunun";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Diğer eylemler";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Ayarı değiştirmek için iki kez dokunun";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Açık";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Kapalı";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ öğe";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, okunmadı";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Diğer";

/* Generic label for cancel action */
"Common.Cancel" = "İptal";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Paylaşılan";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Yalnızca ben";

/* Just now date string */
"Date.Now" = "Şimdi";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm önce";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh önce";

/* Yesterday string */
"Date.Yesterday" = "Dün";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Dün şu saatte: %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@, %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Dizinde Ara";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Başlangıç Saati";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Bitiş Saati";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Başlangıç Tarihi";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Bitiş Tarihi";

/* Presence - Available status */
"Presence.Available" = "Kullanılabilir";

/* Presence - Away status */
"Presence.Away" = "Dışarıda";

/* Presence - Busy status */
"Presence.Busy" = "Meşgul";

/* Presence - Do not disturb status */
"Presence.DND" = "Rahatsız etmeyin";

/* Presence - Out of office status */
"Presence.OOF" = "İş yeri dışında";

/* Presence - Offline status */
"Presence.Offline" = "Çevrimdışı";

/* Presence - Unknown status */
"Presence.Unknown" = "Bilinmiyor";

/* Presence - Blocked status */
"Presence.Blocked" = "Engellendi";
