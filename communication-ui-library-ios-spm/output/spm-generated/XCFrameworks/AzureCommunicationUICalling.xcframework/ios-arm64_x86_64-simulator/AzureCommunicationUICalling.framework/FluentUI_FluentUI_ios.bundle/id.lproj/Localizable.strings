//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "<PERSON>am proses";
"Accessibility.ActivityIndicator.Stopped.label" = "Progres dihentikan";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Peringatan";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Tutup";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Ketuk dua kali untuk menutup";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Selesai";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Ketuk dua kali untuk mematikan/menghidupkan pilihan";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalender";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "<PERSON><PERSON><PERSON> tanggal";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d acara";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "tidak ada acara";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Bulan";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Hari";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Tahun";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Tanggal";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Jam";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Menit";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Minggu dalam sebulan";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Hari dalam seminggu";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Pertama";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Kedua";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Ketiga";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Keempat";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Terakhir";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Perluas";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Ketuk dua kali untuk memperluas";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Ciutkan";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Ketuk dua kali untuk menciutkan";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Selesai";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Gagal";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Memuat";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d dari %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil Akun";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d dari %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Ketuk dua kali untuk melihat tindakan lainnya";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Tindakan lainnya";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Ketuk dua kali untuk memperlihatkan/menyembunyikan pengaturan";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Aktif";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Nonaktif";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ item";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, belum dibaca";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Lainnya";

/* Generic label for cancel action */
"Common.Cancel" = "Batal";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Dibagikan";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Hanya saya";

/* Just now date string */
"Date.Now" = "Baru saja";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm yang lalu";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh yang lalu";

/* Yesterday string */
"Date.Yesterday" = "Kemarin";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Kemarin pada pukul %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ pada %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Cari Direktori";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Waktu Mulai";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Waktu Selesai";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Tanggal Mulai";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Tanggal Selesai";

/* Presence - Available status */
"Presence.Available" = "Online";

/* Presence - Away status */
"Presence.Away" = "Tidak di tempat";

/* Presence - Busy status */
"Presence.Busy" = "Sibuk";

/* Presence - Do not disturb status */
"Presence.DND" = "Jangan ganggu";

/* Presence - Out of office status */
"Presence.OOF" = "Di luar kantor";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Tidak dikenal";

/* Presence - Blocked status */
"Presence.Blocked" = "Diblokir";
