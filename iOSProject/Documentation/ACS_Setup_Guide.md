# Azure Communication Services Setup Guide

This guide will help you configure real Azure Communication Services credentials in your iOS project.

## 🔐 **1. Azure Portal Configuration**

### Step 1: Create Azure Communication Services Resource

1. Go to [Azure Portal](https://portal.azure.com)
2. Click "Create a resource"
3. Search for "Communication Services"
4. Click "Create"
5. Fill in the required information:
   - **Subscription**: Your Azure subscription
   - **Resource Group**: Create new or use existing
   - **Resource Name**: Choose a unique name (e.g., `my-app-acs-prod`)
   - **Data Location**: Choose appropriate region
6. Click "Review + Create" then "Create"

### Step 2: Get Your Credentials

After the resource is created:

1. Go to your Communication Services resource
2. Navigate to "Keys" in the left sidebar
3. Copy the following values:
   - **Connection String** (Primary or Secondary)
   - **Endpoint**
   - **Access Key** (Primary or Secondary)

Example values:
```
Connection String: endpoint=https://my-app-acs-prod.communication.azure.com/;accesskey=abc123...
Endpoint: https://my-app-acs-prod.communication.azure.com/
Access Key: abc123def456...
Resource Name: my-app-acs-prod
```

## 📱 **2. iOS Project Configuration**

### Method 1: Environment Variables (Recommended for Development)

Add these environment variables to your Xcode scheme:

1. In Xcode, go to Product → Scheme → Edit Scheme
2. Select "Run" → "Arguments" → "Environment Variables"
3. Add the following variables:

```
ACS_CONNECTION_STRING = endpoint=https://your-resource.communication.azure.com/;accesskey=your-key
ACS_ENDPOINT = https://your-resource.communication.azure.com/
ACS_ACCESS_KEY = your-access-key
ACS_RESOURCE_NAME = your-resource-name
ACS_TOKEN_SERVICE_URL = https://your-backend.com/api/acs-token
```

### Method 2: Configuration File (Recommended for Production)

1. Open `iOSProject/Core/Resource/ACSConfig.plist`
2. Replace the placeholder values with your real credentials:

```xml
<key>ConnectionString</key>
<string>endpoint=https://your-resource.communication.azure.com/;accesskey=your-key</string>

<key>Endpoint</key>
<string>https://your-resource.communication.azure.com/</string>

<key>AccessKey</key>
<string>your-access-key</string>

<key>ResourceName</key>
<string>your-resource-name</string>
```

### Method 3: Info.plist (Alternative)

Add these keys to your `Info.plist`:

```xml
<key>ACSConnectionString</key>
<string>your-connection-string</string>

<key>ACSEndpoint</key>
<string>your-endpoint</string>

<key>ACSAccessKey</key>
<string>your-access-key</string>

<key>ACSResourceName</key>
<string>your-resource-name</string>
```

## 🔒 **3. Security Best Practices**

### For Development:
- Use environment variables in Xcode schemes
- Never commit real credentials to version control
- Use separate ACS resources for dev/staging/production

### For Production:
- Store credentials in secure configuration files
- Use Azure Key Vault for sensitive data
- Implement token service on your backend
- Never hardcode credentials in source code

### Credential Priority Order:
1. Environment Variables (highest priority)
2. Info.plist
3. ACSConfig.plist bundle
4. Hardcoded values (development only)

## 🏗️ **4. Backend Token Service (Recommended)**

For production apps, implement a backend service to generate ACS tokens:

### Backend Endpoint Example:
```
POST https://your-backend.com/api/acs-token
Content-Type: application/json

{
  "userId": "user123",
  "displayName": "John Doe"
}

Response:
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
  "expiresOn": "2024-01-01T12:00:00Z"
}
```

### Update Token Service URL:
Set the `ACS_TOKEN_SERVICE_URL` environment variable or update `ACSConfig.plist`:

```xml
<key>TokenServiceURL</key>
<string>https://your-backend.com/api/acs-token</string>
```

## 🔄 **5. Environment Switching**

The app automatically detects the environment and switches between mock and real services:

### Development:
- Uses mock service if credentials contain "your-" placeholders
- Uses real service if valid credentials are provided
- Enables detailed logging

### Staging:
- Prefers real service
- Falls back to mock if credentials are invalid
- Enables logging and crash reporting

### Production:
- Always uses real service
- Fails if mock credentials are detected
- Minimal logging for security

## 🧪 **6. Testing Your Configuration**

### Verify Credentials:
```swift
// In your app or unit tests
let isValid = ACSServiceFactory.validateServiceConfiguration()
print("ACS Configuration Valid: \(isValid)")
```

### Health Check:
```swift
Task {
    let isHealthy = await ACSServiceFactory.performHealthCheck()
    print("ACS Service Healthy: \(isHealthy)")
}
```

### Check Current Service Type:
```swift
let service = ACSServiceFactory.createACSService()
if service is MockACSService {
    print("Using Mock ACS Service")
} else {
    print("Using Real ACS Service")
}
```

## 🚨 **7. Troubleshooting**

### Common Issues:

1. **"Configuration Invalid" Error**
   - Check that all credential fields are filled
   - Verify connection string format
   - Ensure endpoint URL is valid

2. **"Token Generation Failed" Error**
   - Check network connectivity
   - Verify backend token service is running
   - Check access key permissions

3. **"Permission Denied" Error**
   - Grant camera/microphone permissions
   - Check iOS privacy settings
   - Verify Info.plist usage descriptions

### Debug Logging:
Enable debug logging to see which service is being used:
```
🔧 Using Mock ACS Service for environment: Development
🚀 Using Real ACS Service for environment: Production
⚠️ Failed to load ACS credentials, falling back to mock service
```

## 📋 **8. Checklist**

Before deploying to production:

- [ ] Real ACS resource created in Azure
- [ ] Credentials properly configured (no "your-" placeholders)
- [ ] Backend token service implemented and tested
- [ ] Camera/microphone permissions configured
- [ ] Environment-specific configurations tested
- [ ] Security review completed
- [ ] Credentials not committed to version control

## 🔗 **9. Additional Resources**

- [Azure Communication Services Documentation](https://docs.microsoft.com/en-us/azure/communication-services/)
- [ACS iOS SDK Documentation](https://docs.microsoft.com/en-us/azure/communication-services/quickstarts/ui-library/get-started-composites?tabs=kotlin&pivots=platform-ios)
- [ACS Token Service Tutorial](https://docs.microsoft.com/en-us/azure/communication-services/tutorials/trusted-service-tutorial)
