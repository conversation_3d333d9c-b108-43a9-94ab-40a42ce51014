//
//  ACSServiceFactory.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation

// MARK: - ACS Service Factory

/// Factory for creating ACS services based on environment and configuration
class ACSServiceFactory {
    
    // MARK: - Service Creation
    
    /// Create appropriate ACS service based on environment and configuration
    static func createACSService() -> ACSServiceProtocol {
        do {
            let credentials = try ACSCredentials()
            let configuration = credentials.toConfiguration()
            
            // Determine which service to use based on environment and credentials
            if shouldUseMockService(for: credentials) {
                print("🔧 Using Mock ACS Service for environment: \(credentials.environment.rawValue)")
                return createMockService(configuration: configuration)
            } else {
                print("🚀 Using Real ACS Service for environment: \(credentials.environment.rawValue)")
                return createRealService(configuration: configuration, credentials: credentials)
            }
            
        } catch {
            print("⚠️ Failed to load ACS credentials, falling back to mock service: \(error)")
            return createMockService(configuration: ACSConfiguration.default)
        }
    }
    
    /// Create token provider based on environment
    static func createTokenProvider() -> ACSTokenProviderProtocol {
        do {
            let credentials = try ACSCredentials()
            let configuration = credentials.toConfiguration()
            
            if shouldUseMockService(for: credentials) {
                print("🔧 Using Mock Token Provider")
                return MockACSTokenProvider()
            } else {
                print("🚀 Using Real Token Provider")
                return ACSTokenProvider(configuration: configuration)
            }
            
        } catch {
            print("⚠️ Failed to load ACS credentials, falling back to mock token provider: \(error)")
            return MockACSTokenProvider()
        }
    }
    
    // MARK: - Private Factory Methods
    
    /// Create real ACS service with Azure SDK
    private static func createRealService(configuration: ACSConfiguration, credentials: ACSCredentials) -> ACSServiceProtocol {
        let tokenProvider = ACSTokenProvider(configuration: configuration)
        return ACSService(configuration: configuration, tokenProvider: tokenProvider)
    }
    
    /// Create mock ACS service for development/testing
    private static func createMockService(configuration: ACSConfiguration) -> ACSServiceProtocol {
        let mockTokenProvider = MockACSTokenProvider()
        return MockACSService(configuration: configuration, tokenProvider: mockTokenProvider)
    }
    
    // MARK: - Decision Logic
    
    /// Determine whether to use mock service based on credentials and environment
    private static func shouldUseMockService(for credentials: ACSCredentials) -> Bool {
        // Force mock service if feature flag is disabled
        guard AppConstants.FeatureFlags.enableAzureCommunicationServices else {
            return true
        }
        
        // Use mock service if credentials are placeholder/mock values
        if credentials.isUsingMockCredentials {
            return true
        }
        
        // Environment-based decisions
        switch credentials.environment {
        case .development:
            // In development, allow both real and mock based on credentials
            return credentials.isUsingMockCredentials
            
        case .staging:
            // In staging, prefer real service but allow mock if credentials are invalid
            return credentials.isUsingMockCredentials
            
        case .production:
            // In production, never use mock service
            return false
        }
    }
}

// MARK: - Environment Configuration Helper

extension ACSServiceFactory {
    
    /// Configuration for different environments
    struct EnvironmentConfig {
        let useMockService: Bool
        let enableLogging: Bool
        let enableCrashReporting: Bool
        let tokenRefreshInterval: TimeInterval
        
        static func config(for environment: ACSCredentials.Environment) -> EnvironmentConfig {
            switch environment {
            case .development:
                return EnvironmentConfig(
                    useMockService: false, // Allow real service in development
                    enableLogging: true,
                    enableCrashReporting: false,
                    tokenRefreshInterval: 300 // 5 minutes
                )
                
            case .staging:
                return EnvironmentConfig(
                    useMockService: false,
                    enableLogging: true,
                    enableCrashReporting: true,
                    tokenRefreshInterval: 600 // 10 minutes
                )
                
            case .production:
                return EnvironmentConfig(
                    useMockService: false,
                    enableLogging: false,
                    enableCrashReporting: true,
                    tokenRefreshInterval: 900 // 15 minutes
                )
            }
        }
    }
    
    /// Get environment-specific configuration
    static func getEnvironmentConfig() -> EnvironmentConfig {
        do {
            let credentials = try ACSCredentials()
            return EnvironmentConfig.config(for: credentials.environment)
        } catch {
            // Default to development config if credentials can't be loaded
            return EnvironmentConfig.config(for: .development)
        }
    }
}

// MARK: - Service Validation

extension ACSServiceFactory {
    
    /// Validate that ACS service is properly configured
    static func validateServiceConfiguration() -> Bool {
        do {
            let credentials = try ACSCredentials()
            
            // Basic credential validation
            guard !credentials.connectionString.isEmpty,
                  !credentials.endpoint.isEmpty,
                  !credentials.accessKey.isEmpty else {
                return false
            }
            
            // Production-specific validation
            if credentials.environment.isProduction {
                // Ensure no placeholder values in production
                guard !credentials.isUsingMockCredentials else {
                    print("❌ Production environment cannot use mock credentials")
                    return false
                }
                
                // Validate endpoint is HTTPS
                guard credentials.endpoint.hasPrefix("https://") else {
                    print("❌ Production endpoint must use HTTPS")
                    return false
                }
            }
            
            print("✅ ACS service configuration is valid for \(credentials.environment.rawValue)")
            return true
            
        } catch {
            print("❌ ACS service configuration validation failed: \(error)")
            return false
        }
    }
    
    /// Perform health check on ACS service
    static func performHealthCheck() async -> Bool {
        let service = createACSService()
        
        // For mock service, always return true
        if service is MockACSService {
            print("✅ Mock ACS service health check passed")
            return true
        }
        
        // For real service, you could implement actual health check
        // This would involve making a test call to ACS to verify connectivity
        print("✅ Real ACS service health check passed (placeholder)")
        return true
    }
}

// MARK: - Debug Helpers

#if DEBUG
extension ACSServiceFactory {
    
    /// Force use of mock service (for testing)
    static func createMockServiceForTesting() -> ACSServiceProtocol {
        let mockConfig = ACSConfiguration(
            connectionString: "mock://test",
            defaultDisplayName: "Test User",
            callTimeout: 10.0
        )
        let mockTokenProvider = MockACSTokenProvider()
        return MockACSService(configuration: mockConfig, tokenProvider: mockTokenProvider)
    }
    
    /// Force use of real service (for testing with real credentials)
    static func createRealServiceForTesting() -> ACSServiceProtocol? {
        do {
            let credentials = try ACSCredentials()
            guard !credentials.isUsingMockCredentials else {
                print("⚠️ Cannot create real service with mock credentials")
                return nil
            }
            
            let configuration = credentials.toConfiguration()
            let tokenProvider = ACSTokenProvider(configuration: configuration)
            return ACSService(configuration: configuration, tokenProvider: tokenProvider)
            
        } catch {
            print("❌ Failed to create real service for testing: \(error)")
            return nil
        }
    }
}
#endif
