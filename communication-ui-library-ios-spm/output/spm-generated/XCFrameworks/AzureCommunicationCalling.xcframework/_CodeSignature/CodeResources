<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/ACSMetalRenderer.metallib</key>
		<data>
		TXoNy8Oo+BnpQQ/DR/UmZYg1DKE=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/AzureCommunicationCalling</key>
		<data>
		1H4CEgH4qkD9PL9UX7Qp+u1DNBw=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSCallKit.h</key>
		<data>
		CBmikGgNCo5gtju66l7FT5FA1Sw=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSFeatures.h</key>
		<data>
		jaRkVxk3BIgwXQfziRjFfy29H/s=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h</key>
		<data>
		D9DwOD9+vaSTfi3sgw1BD2sIfIg=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h</key>
		<data>
		r97E81yVwan0gp4RXqp0SqbByjU=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h</key>
		<data>
		QX4k0LZ4ArjTnzI8Jqd+Gkw/5Sc=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h</key>
		<data>
		fiTFvtMZX48M4dSR0jUthLwvMhc=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h</key>
		<data>
		wph/Iwqq77EccfT9J9YX0mUr1bI=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Info.plist</key>
		<data>
		dHjxrmxIBFfRI2FyY3zuWQGx7BM=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<data>
		GnNh3Azhvp/WP0XglgQQUwWvCEA=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.abi.json</key>
		<data>
		DT9kr0hSNFxd2lAJ5xBN1cgH2vY=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<data>
		DT15+JnonVrBpRyrPU7Rlc1XIos=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<data>
		ebnY0KIOBJjwPozGfMNTHBe/kXg=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<data>
		DT15+JnonVrBpRyrPU7Rlc1XIos=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/module.modulemap</key>
		<data>
		OeiKbCxs0g/VhZ30DZmnJVUU0Ik=
		</data>
		<key>ios-arm64/AzureCommunicationCalling.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Cl8yIx4MGN+Ni4+JQGhbWYPz/tQ=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/ACSMetalRenderer.metallib</key>
		<data>
		f8feHq815OWUdf5MGUltwogjJFw=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/AzureCommunicationCalling</key>
		<data>
		ytZmZb67oeBnoA/ubNQBwV/F4vA=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSCallKit.h</key>
		<data>
		CBmikGgNCo5gtju66l7FT5FA1Sw=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSFeatures.h</key>
		<data>
		jaRkVxk3BIgwXQfziRjFfy29H/s=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h</key>
		<data>
		D9DwOD9+vaSTfi3sgw1BD2sIfIg=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h</key>
		<data>
		r97E81yVwan0gp4RXqp0SqbByjU=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h</key>
		<data>
		QX4k0LZ4ArjTnzI8Jqd+Gkw/5Sc=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h</key>
		<data>
		laL5+vr8OFtkY5olMMNRzhJZkP0=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h</key>
		<data>
		wph/Iwqq77EccfT9J9YX0mUr1bI=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Info.plist</key>
		<data>
		WUujbjLEODj831AHpoTmt+C4EUM=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		5OCKoClLRFRg+t4Yt/pVCwtz5uo=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		FRzfPWbJD2WmB+4noFaU8nPtLpE=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<data>
		DT9kr0hSNFxd2lAJ5xBN1cgH2vY=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		83l7C6WSgjO17faKsOuihHaWvAo=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<data>
		bPvyvy9j4r+k3lvWg111pV6bk/c=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<data>
		83l7C6WSgjO17faKsOuihHaWvAo=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		+PWureK7SiktcADSlndl30G0dCg=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		FdRgf7OqCXw8LTEJDTGPBu8BcS4=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		4piqktidTSb1sZhxIXLbd/DyIYw=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		FdRgf7OqCXw8LTEJDTGPBu8BcS4=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/module.modulemap</key>
		<data>
		OeiKbCxs0g/VhZ30DZmnJVUU0Ik=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/PrivacyInfo.xcprivacy</key>
		<data>
		Cl8yIx4MGN+Ni4+JQGhbWYPz/tQ=
		</data>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/_CodeSignature/CodeResources</key>
		<data>
		jzQ4uTe/Hk0t2oxqBgwrX0hH45o=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/ACSMetalRenderer.metallib</key>
		<dict>
			<key>hash</key>
			<data>
			TXoNy8Oo+BnpQQ/DR/UmZYg1DKE=
			</data>
			<key>hash2</key>
			<data>
			8+nDWPOMhH5DQyxu6zmeU2sOPhCq9UBryPyquGw/aAg=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/AzureCommunicationCalling</key>
		<dict>
			<key>hash</key>
			<data>
			1H4CEgH4qkD9PL9UX7Qp+u1DNBw=
			</data>
			<key>hash2</key>
			<data>
			gX775ztHMDo8voqtEJMUZBo/nb9tGQ+nEs2VvZT7REU=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSCallKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			CBmikGgNCo5gtju66l7FT5FA1Sw=
			</data>
			<key>hash2</key>
			<data>
			YnqjK1WAcaA/idv+5Ayq4pBTqSLtp9Vv4aK6cB+wYVY=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSFeatures.h</key>
		<dict>
			<key>hash</key>
			<data>
			jaRkVxk3BIgwXQfziRjFfy29H/s=
			</data>
			<key>hash2</key>
			<data>
			XHzbTxEB1rLy93iVxn+rdoTjF3W9JIOGXXV3h7YpxbI=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			D9DwOD9+vaSTfi3sgw1BD2sIfIg=
			</data>
			<key>hash2</key>
			<data>
			yO8RLdc3eOYM/vUn6xD0L+6g3JedzgOZnF/HEIGBl4c=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			r97E81yVwan0gp4RXqp0SqbByjU=
			</data>
			<key>hash2</key>
			<data>
			2OBhpniKULwdjPtVzfylW2N/hAx24HfJrc6/+TjaMBI=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h</key>
		<dict>
			<key>hash</key>
			<data>
			QX4k0LZ4ArjTnzI8Jqd+Gkw/5Sc=
			</data>
			<key>hash2</key>
			<data>
			HPXgOrO8AH6977OdmDtf0ITOQ2KUtYydL5lwXUGUYVc=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			fiTFvtMZX48M4dSR0jUthLwvMhc=
			</data>
			<key>hash2</key>
			<data>
			3FaHfr4g4wd3spikn2wpKtXNcJosvOMWRCIb9p0j+3U=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h</key>
		<dict>
			<key>hash</key>
			<data>
			wph/Iwqq77EccfT9J9YX0mUr1bI=
			</data>
			<key>hash2</key>
			<data>
			k+pMdqqjLABA2+QTQY0SZtybJSRl6y6WtZqjzcm5K8g=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			dHjxrmxIBFfRI2FyY3zuWQGx7BM=
			</data>
			<key>hash2</key>
			<data>
			PsvRLjim0ngdSgdYBS0OFiuHWoZ8XFjhcGOWJEmEMqs=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			GnNh3Azhvp/WP0XglgQQUwWvCEA=
			</data>
			<key>hash2</key>
			<data>
			rweTd65fEcxyKvClq3JTKMZWPuM5NMI0EEf5fI+bfKY=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			DT9kr0hSNFxd2lAJ5xBN1cgH2vY=
			</data>
			<key>hash2</key>
			<data>
			uWbtuUKI5ODxFxkGl4HHsNHZnhJ2I0szxPoHoBPREOw=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			DT15+JnonVrBpRyrPU7Rlc1XIos=
			</data>
			<key>hash2</key>
			<data>
			LtTxCEwII6zWaQGdZhR5QyJPyQE9KTOHMnV7UDDgSpk=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			ebnY0KIOBJjwPozGfMNTHBe/kXg=
			</data>
			<key>hash2</key>
			<data>
			cVoF/Q+1HYbDsDmIJhlMpacRTv0LvoW07UaqI5nIOcQ=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			DT15+JnonVrBpRyrPU7Rlc1XIos=
			</data>
			<key>hash2</key>
			<data>
			LtTxCEwII6zWaQGdZhR5QyJPyQE9KTOHMnV7UDDgSpk=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			OeiKbCxs0g/VhZ30DZmnJVUU0Ik=
			</data>
			<key>hash2</key>
			<data>
			LORdCOLnvKsRfQ4FxDs6bhy1Q9Gw2ifntkGDem/i5U4=
			</data>
		</dict>
		<key>ios-arm64/AzureCommunicationCalling.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Cl8yIx4MGN+Ni4+JQGhbWYPz/tQ=
			</data>
			<key>hash2</key>
			<data>
			M9Imm1S0XYFDmUbOb5cQj9PwTNj+UOgF681ZBia359Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/ACSMetalRenderer.metallib</key>
		<dict>
			<key>hash</key>
			<data>
			f8feHq815OWUdf5MGUltwogjJFw=
			</data>
			<key>hash2</key>
			<data>
			k9fm7isfPA1WkqPhAEH4VlDaAmjJ7tOhgXY57oIi/cA=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/AzureCommunicationCalling</key>
		<dict>
			<key>hash</key>
			<data>
			ytZmZb67oeBnoA/ubNQBwV/F4vA=
			</data>
			<key>hash2</key>
			<data>
			JICvYRHgPIivnh8eC9AKCwPasJenoyaCcCsL5/pW0w4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSCallKit.h</key>
		<dict>
			<key>hash</key>
			<data>
			CBmikGgNCo5gtju66l7FT5FA1Sw=
			</data>
			<key>hash2</key>
			<data>
			YnqjK1WAcaA/idv+5Ayq4pBTqSLtp9Vv4aK6cB+wYVY=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSFeatures.h</key>
		<dict>
			<key>hash</key>
			<data>
			jaRkVxk3BIgwXQfziRjFfy29H/s=
			</data>
			<key>hash2</key>
			<data>
			XHzbTxEB1rLy93iVxn+rdoTjF3W9JIOGXXV3h7YpxbI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSStreamSize.h</key>
		<dict>
			<key>hash</key>
			<data>
			D9DwOD9+vaSTfi3sgw1BD2sIfIg=
			</data>
			<key>hash2</key>
			<data>
			yO8RLdc3eOYM/vUn6xD0L+6g3JedzgOZnF/HEIGBl4c=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRenderer.h</key>
		<dict>
			<key>hash</key>
			<data>
			r97E81yVwan0gp4RXqp0SqbByjU=
			</data>
			<key>hash2</key>
			<data>
			2OBhpniKULwdjPtVzfylW2N/hAx24HfJrc6/+TjaMBI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/ACSVideoStreamRendererView.h</key>
		<dict>
			<key>hash</key>
			<data>
			QX4k0LZ4ArjTnzI8Jqd+Gkw/5Sc=
			</data>
			<key>hash2</key>
			<data>
			HPXgOrO8AH6977OdmDtf0ITOQ2KUtYydL5lwXUGUYVc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling-Swift.h</key>
		<dict>
			<key>hash</key>
			<data>
			laL5+vr8OFtkY5olMMNRzhJZkP0=
			</data>
			<key>hash2</key>
			<data>
			bredOiV89ebGPPaC5XUfbb5kK8CTsTETvWCTzUZeftk=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Headers/AzureCommunicationCalling.h</key>
		<dict>
			<key>hash</key>
			<data>
			wph/Iwqq77EccfT9J9YX0mUr1bI=
			</data>
			<key>hash2</key>
			<data>
			k+pMdqqjLABA2+QTQY0SZtybJSRl6y6WtZqjzcm5K8g=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Info.plist</key>
		<dict>
			<key>hash</key>
			<data>
			WUujbjLEODj831AHpoTmt+C4EUM=
			</data>
			<key>hash2</key>
			<data>
			zFCgnxXQh6eZ6jH3paeUopsG9kLT6Zc+dM2NZCPrA+w=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/arm64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			5OCKoClLRFRg+t4Yt/pVCwtz5uo=
			</data>
			<key>hash2</key>
			<data>
			YgbjBpRHXXPGHQ8Xub1iUxtcelDZtVR6Do4EHJ8il9I=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash</key>
			<data>
			FRzfPWbJD2WmB+4noFaU8nPtLpE=
			</data>
			<key>hash2</key>
			<data>
			nsEKGgbViOfZKZx4Onzn+2qrxfN/z/W6fb+e29BAQiI=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			DT9kr0hSNFxd2lAJ5xBN1cgH2vY=
			</data>
			<key>hash2</key>
			<data>
			uWbtuUKI5ODxFxkGl4HHsNHZnhJ2I0szxPoHoBPREOw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			83l7C6WSgjO17faKsOuihHaWvAo=
			</data>
			<key>hash2</key>
			<data>
			jZcIqX3Vtb9CxRuhFq7CIJD0SmUfx/X37koRPMOFCLw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			bPvyvy9j4r+k3lvWg111pV6bk/c=
			</data>
			<key>hash2</key>
			<data>
			ogbBBX+j/1D3fmLqzgGkBeW8DSEssq/dmJLDQlROFFc=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/arm64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			83l7C6WSgjO17faKsOuihHaWvAo=
			</data>
			<key>hash2</key>
			<data>
			jZcIqX3Vtb9CxRuhFq7CIJD0SmUfx/X37koRPMOFCLw=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash</key>
			<data>
			+PWureK7SiktcADSlndl30G0dCg=
			</data>
			<key>hash2</key>
			<data>
			fEP6jem+1jxa/MXxq2cZKG7dhE5XCigYn1EUt4KwD4Q=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			FdRgf7OqCXw8LTEJDTGPBu8BcS4=
			</data>
			<key>hash2</key>
			<data>
			n7qlvFaukJHZlDQNphgsZveyA3sLtmKnJtXluJX+T3k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash</key>
			<data>
			4piqktidTSb1sZhxIXLbd/DyIYw=
			</data>
			<key>hash2</key>
			<data>
			SDbqOilX2M99kO/QcmJsxlaeYirI9RjicRexgHH4YbM=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash</key>
			<data>
			FdRgf7OqCXw8LTEJDTGPBu8BcS4=
			</data>
			<key>hash2</key>
			<data>
			n7qlvFaukJHZlDQNphgsZveyA3sLtmKnJtXluJX+T3k=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/Modules/module.modulemap</key>
		<dict>
			<key>hash</key>
			<data>
			OeiKbCxs0g/VhZ30DZmnJVUU0Ik=
			</data>
			<key>hash2</key>
			<data>
			LORdCOLnvKsRfQ4FxDs6bhy1Q9Gw2ifntkGDem/i5U4=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash</key>
			<data>
			Cl8yIx4MGN+Ni4+JQGhbWYPz/tQ=
			</data>
			<key>hash2</key>
			<data>
			M9Imm1S0XYFDmUbOb5cQj9PwTNj+UOgF681ZBia359Y=
			</data>
		</dict>
		<key>ios-arm64_x86_64-simulator/AzureCommunicationCalling.framework/_CodeSignature/CodeResources</key>
		<dict>
			<key>hash</key>
			<data>
			jzQ4uTe/Hk0t2oxqBgwrX0hH45o=
			</data>
			<key>hash2</key>
			<data>
			DCzhsQ2sjFKpwMZ+SryWj4OduPmrnnJJ3adbSdgr3fU=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
