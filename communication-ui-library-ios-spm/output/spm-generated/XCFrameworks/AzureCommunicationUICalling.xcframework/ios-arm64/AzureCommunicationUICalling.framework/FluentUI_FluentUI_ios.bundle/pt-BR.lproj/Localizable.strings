//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Em andamento";
"Accessibility.ActivityIndicator.Stopped.label" = "Andamento interrompido";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alerta";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Ignorar";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dê um toque duplo para ignorar";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Concluída";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dê um toque duplo para alternar a seleção";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendário";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Selecionar uma data";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d eventos";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "nenhum evento";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mês";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dia";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Ano";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Data";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hora";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuto";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "Manhã/Tarde";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Semana do mês";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dia da semana";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Primeira";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Segunda";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Terceira";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Quarta";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Última";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Expandir";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dê um toque duplo para expandir";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Recolher";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dê um toque duplo para recolher";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Concluída";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Falhou";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Carregando";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d de %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Perfil da Conta";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d de %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dê um toque duplo para exibir mais ações";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Mais ações";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dê um toque duplo para ativar/desativar a configuração";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Ativar";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Desativar";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ ítens";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, não lido(s)";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Mais";

/* Generic label for cancel action */
"Common.Cancel" = "Cancelar";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Compartilhado";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Apenas eu";

/* Just now date string */
"Date.Now" = "Agora mesmo";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm atrás";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh atrás";

/* Yesterday string */
"Date.Yesterday" = "Ontem";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Ontem às %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ às %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Diretório de Pesquisa";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Hora de Início";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Hora de Término";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Data de Início";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Data de Término";

/* Presence - Available status */
"Presence.Available" = "Disponível";

/* Presence - Away status */
"Presence.Away" = "Ausente";

/* Presence - Busy status */
"Presence.Busy" = "Ocupado";

/* Presence - Do not disturb status */
"Presence.DND" = "Não incomodar";

/* Presence - Out of office status */
"Presence.OOF" = "Ausência temporária";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Desconhecido";

/* Presence - Blocked status */
"Presence.Blocked" = "Bloqueado";
