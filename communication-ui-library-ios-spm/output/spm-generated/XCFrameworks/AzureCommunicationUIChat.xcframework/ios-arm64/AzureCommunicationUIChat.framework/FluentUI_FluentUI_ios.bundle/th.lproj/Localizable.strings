//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "กำลังดำเนินการ";
"Accessibility.ActivityIndicator.Stopped.label" = "หยุดความคืบหน้าแล้ว";

/* Accessibility alert for common use */
"Accessibility.Alert" = "การแจ้งเตือน";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "ยกเลิก";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "แตะสองครั้งเพื่อยกเลิก";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "เสร็จสิ้น";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "แตะสองครั้งเพื่อสลับการเลือก ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "ปฏิทิน";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "เลือกวันที่";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d เหตุการณ์";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "ไม่มีเหตุการณ์";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "เดือน";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "วัน";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "ปี";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "วันที่";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "ชั่วโมง";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "นาที";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "สัปดาห์ของเดือน";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "วันในสัปดาห์";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "แรก";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "ที่สอง";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "ที่สาม";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "ที่สี่";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "สุดท้าย";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "ขยาย";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "แตะสองครั้งเพื่อขยาย";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "ยุบ";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "แตะสองครั้งเพื่อยุบ";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "เสร็จสิ้น";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "ล้มเหลว";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "กำลังโหลด";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d จาก %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "โปรไฟล์บัญชี";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d จาก %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "แตะสองครั้งเพื่อดูการดำเนินการเพิ่มเติม";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "การดำเนินการเพิ่มเติม";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "แตะสองครั้งเพื่อสลับการตั้งค่า";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "เปิด";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "ปิด";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ รายการ";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@ ยังไม่ได้อ่าน";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "เพิ่มเติม";

/* Generic label for cancel action */
"Common.Cancel" = "ยกเลิก";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "แชร์แล้ว";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "ฉันเท่านั้น";

/* Just now date string */
"Date.Now" = "เมื่อครู่นี้";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm ที่แล้ว";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh ที่แล้ว";

/* Yesterday string */
"Date.Yesterday" = "เมื่อวานนี้";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "เมื่อวาน เวลา %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ เวลา %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "ค้นหาในไดเรกทอรี";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "เวลาเริ่มต้น";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "เวลาสิ้นสุด";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "วันที่เริ่มต้น";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "วันที่สิ้นสุด";

/* Presence - Available status */
"Presence.Available" = "ว่าง";

/* Presence - Away status */
"Presence.Away" = "ไม่อยู่";

/* Presence - Busy status */
"Presence.Busy" = "ไม่ว่าง";

/* Presence - Do not disturb status */
"Presence.DND" = "ห้ามรบกวน";

/* Presence - Out of office status */
"Presence.OOF" = "ไม่อยู่ที่สำนักงาน";

/* Presence - Offline status */
"Presence.Offline" = "ออฟไลน์";

/* Presence - Unknown status */
"Presence.Unknown" = "ไม่รู้จัก";

/* Presence - Blocked status */
"Presence.Blocked" = "บล็อกอยู่";
