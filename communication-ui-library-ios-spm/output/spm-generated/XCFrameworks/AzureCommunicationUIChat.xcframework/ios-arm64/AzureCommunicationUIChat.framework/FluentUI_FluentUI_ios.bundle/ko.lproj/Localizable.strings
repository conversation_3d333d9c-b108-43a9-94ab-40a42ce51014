//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "진행 중";
"Accessibility.ActivityIndicator.Stopped.label" = "진행이 중지되었습니다";

/* Accessibility alert for common use */
"Accessibility.Alert" = "알림";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "해제";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "해제하려면 두 번 탭하세요.";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "완료";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "두 번 탭하여 선택을 전환할 수 있습니다. ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "일정";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "날짜 선택";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "이벤트 %d개";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "이벤트 없음";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "월";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "일";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "연도";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "날짜";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "시간";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "분";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "오전/오후";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "주";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "요일";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "첫째 주";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "둘째 주";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "셋째 주";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "넷째 주";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "마지막 주";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "확장";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "확장하려면 두 번 탭하세요.";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "축소";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "축소하려면 두 번 탭하세요.";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "완료";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "실패";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "로드 중";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d/%2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "계정 프로필";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d/%2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "더 많은 작업을 보려면 두 번 탭하세요.";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "추가 작업";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "두 번 탭하여 전환";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "설정";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "끔";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ 항목";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, 읽지 않음";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "더 보기";

/* Generic label for cancel action */
"Common.Cancel" = "취소";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "공유됨";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "나만";

/* Just now date string */
"Date.Now" = "오늘";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm 전";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh 전";

/* Yesterday string */
"Date.Yesterday" = "어제";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "어제 %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "디렉터리 검색";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "시작 시간";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "종료 시간";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "시작 날짜";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "종료 날짜";

/* Presence - Available status */
"Presence.Available" = "사용 가능";

/* Presence - Away status */
"Presence.Away" = "자리 비움";

/* Presence - Busy status */
"Presence.Busy" = "약속 있음";

/* Presence - Do not disturb status */
"Presence.DND" = "방해 금지";

/* Presence - Out of office status */
"Presence.OOF" = "부재 중";

/* Presence - Offline status */
"Presence.Offline" = "오프라인";

/* Presence - Unknown status */
"Presence.Unknown" = "알 수 없음";

/* Presence - Blocked status */
"Presence.Blocked" = "차단됨";
