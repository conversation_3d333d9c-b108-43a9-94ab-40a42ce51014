//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "In Bearbeitung";
"Accessibility.ActivityIndicator.Stopped.label" = "Bearbeitung angehalten";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Warnung";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Schließen";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Zum Schließen doppeltippen.";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Fertig";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Zum Umschalten der Auswahl doppelt tippen";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalender";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Datum auswählen";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d Ereignisse";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "Keine Ereignisse";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Monat";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Tag";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Jahr";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Datum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Stunde";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minute";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Woche im Monat";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Tag der Woche";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Erste";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Zweite";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Dritte";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Vierte";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Letzte";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Erweitern";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Zum Erweitern doppeltippen";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Reduzieren";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Zum Reduzieren doppeltippen";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Fertig";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Fehler";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Wird geladen";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d von %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Kontoprofil";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d von %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Doppeltippen Sie, um weitere Aktionen anzuzeigen.";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Weitere Aktionen";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Doppeltippen Sie, um die Einstellung umzuschalten.";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Ein";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Aus";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ Elemente";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, ungelesen";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Mehr";

/* Generic label for cancel action */
"Common.Cancel" = "Abbrechen";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Freigegeben";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Nur ich";

/* Just now date string */
"Date.Now" = "Gerade eben";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Vor %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Vor %ldh";

/* Yesterday string */
"Date.Yesterday" = "Gestern";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Gestern um %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ um %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Verzeichnis durchsuchen";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Startzeit";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Endzeit";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Startdatum";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Enddatum";

/* Presence - Available status */
"Presence.Available" = "Verfügbar";

/* Presence - Away status */
"Presence.Away" = "Abwesend";

/* Presence - Busy status */
"Presence.Busy" = "Beschäftigt";

/* Presence - Do not disturb status */
"Presence.DND" = "Nicht stören";

/* Presence - Out of office status */
"Presence.OOF" = "Außer Haus";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Unbekannt";

/* Presence - Blocked status */
"Presence.Blocked" = "Blockiert";
