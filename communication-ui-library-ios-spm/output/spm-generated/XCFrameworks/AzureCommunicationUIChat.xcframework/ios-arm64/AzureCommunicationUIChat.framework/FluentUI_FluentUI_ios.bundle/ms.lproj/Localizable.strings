//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Sedang berjalan";
"Accessibility.ActivityIndicator.Stopped.label" = "Kemajuan dihentikan";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Isyarat";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Tolak";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dwiketik untuk menolak";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Selesai";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dwiketik untuk togol pilihan";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalendar";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Pilih tarikh";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d peristiwa";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "tiada peristiwa";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Bulan";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Hari";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Tahun";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Tarikh";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Jam";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minit";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Minggu dalam bulan";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Hari dalam minggu";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Pertama";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Kedua";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Ketiga";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Keempat";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Terakhir";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Kembangkan";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dwiketik untuk mengembangkan";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Runtuhkan";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dwiketik untuk meruntuhkan";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Selesai";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Gagal";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Memuatkan";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d daripada %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil Akaun";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d daripada %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dwiketik untuk melihat lebih banyak tindakan";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Lagi tindakan";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dwiketik untuk menogol seting";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Hidupkan";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Matikan";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ item";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, tidak dibaca";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Selanjutnya";

/* Generic label for cancel action */
"Common.Cancel" = "Batal";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Dikongsi";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Hanya saya";

/* Just now date string */
"Date.Now" = "Tadi";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm yang lalu";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh yang lalu";

/* Yesterday string */
"Date.Yesterday" = "Semalam";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Semalam di %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ pada %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Cari dalam Direktori";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Masa Mula";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Waktu Tamat";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Tarikh Mula";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Tarikh Tamat";

/* Presence - Available status */
"Presence.Available" = "Tersedia";

/* Presence - Away status */
"Presence.Away" = "Tiada";

/* Presence - Busy status */
"Presence.Busy" = "Sibuk";

/* Presence - Do not disturb status */
"Presence.DND" = "Jangan ganggu";

/* Presence - Out of office status */
"Presence.OOF" = "Luar dari pejabat";

/* Presence - Offline status */
"Presence.Offline" = "Luar talian";

/* Presence - Unknown status */
"Presence.Unknown" = "Tidak Diketahui";

/* Presence - Blocked status */
"Presence.Blocked" = "Disekat";
