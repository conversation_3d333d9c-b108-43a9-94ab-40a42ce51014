#!/bin/bash

# Azure Communication Services Environment Setup Script
echo "🔐 Azure Communication Services Environment Setup"
echo "================================================="

# Configuration
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
CONFIG_FILE="$PROJECT_ROOT/Core/Resource/ACSConfig.plist"

echo "This script helps you configure ACS credentials securely."
echo "Please refer to the setup guide in Documentation/ACS_Setup_Guide.md"
echo ""
echo "Configuration file location: $CONFIG_FILE"
echo ""
echo "To configure your credentials:"
echo "1. Edit $CONFIG_FILE with your real Azure credentials"
echo "2. Or set environment variables in your Xcode scheme"
echo "3. See Documentation/ACS_Setup_Guide.md for detailed instructions"

