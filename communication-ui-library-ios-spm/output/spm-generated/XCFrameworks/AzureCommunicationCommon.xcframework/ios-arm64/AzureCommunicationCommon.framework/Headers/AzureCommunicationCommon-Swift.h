#if 0
#elif defined(__arm64__) && __arm64__
// Generated by Apple Swift version 6.1.2 effective-5.10 (swiftlang-6.1.2.1.2 clang-1700.0.13.5)
#ifndef AZURECOMMUNICATIONCOMMON_SWIFT_H
#define AZURECOMMUNICATIONCOMMON_SWIFT_H
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wgcc-compat"

#if !defined(__has_include)
# define __has_include(x) 0
#endif
#if !defined(__has_attribute)
# define __has_attribute(x) 0
#endif
#if !defined(__has_feature)
# define __has_feature(x) 0
#endif
#if !defined(__has_warning)
# define __has_warning(x) 0
#endif

#if __has_include(<swift/objc-prologue.h>)
# include <swift/objc-prologue.h>
#endif

#pragma clang diagnostic ignored "-Wauto-import"
#if defined(__OBJC__)
#include <Foundation/Foundation.h>
#endif
#if defined(__cplusplus)
#include <cstdint>
#include <cstddef>
#include <cstdbool>
#include <cstring>
#include <stdlib.h>
#include <new>
#include <type_traits>
#else
#include <stdint.h>
#include <stddef.h>
#include <stdbool.h>
#include <string.h>
#endif
#if defined(__cplusplus)
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wnon-modular-include-in-framework-module"
#if defined(__arm64e__) && __has_include(<ptrauth.h>)
# include <ptrauth.h>
#else
#pragma clang diagnostic push
#pragma clang diagnostic ignored "-Wreserved-macro-identifier"
# ifndef __ptrauth_swift_value_witness_function_pointer
#  define __ptrauth_swift_value_witness_function_pointer(x)
# endif
# ifndef __ptrauth_swift_class_method_pointer
#  define __ptrauth_swift_class_method_pointer(x)
# endif
#pragma clang diagnostic pop
#endif
#pragma clang diagnostic pop
#endif

#if !defined(SWIFT_TYPEDEFS)
# define SWIFT_TYPEDEFS 1
# if __has_include(<uchar.h>)
#  include <uchar.h>
# elif !defined(__cplusplus)
typedef unsigned char char8_t;
typedef uint_least16_t char16_t;
typedef uint_least32_t char32_t;
# endif
typedef float swift_float2  __attribute__((__ext_vector_type__(2)));
typedef float swift_float3  __attribute__((__ext_vector_type__(3)));
typedef float swift_float4  __attribute__((__ext_vector_type__(4)));
typedef double swift_double2  __attribute__((__ext_vector_type__(2)));
typedef double swift_double3  __attribute__((__ext_vector_type__(3)));
typedef double swift_double4  __attribute__((__ext_vector_type__(4)));
typedef int swift_int2  __attribute__((__ext_vector_type__(2)));
typedef int swift_int3  __attribute__((__ext_vector_type__(3)));
typedef int swift_int4  __attribute__((__ext_vector_type__(4)));
typedef unsigned int swift_uint2  __attribute__((__ext_vector_type__(2)));
typedef unsigned int swift_uint3  __attribute__((__ext_vector_type__(3)));
typedef unsigned int swift_uint4  __attribute__((__ext_vector_type__(4)));
#endif

#if !defined(SWIFT_PASTE)
# define SWIFT_PASTE_HELPER(x, y) x##y
# define SWIFT_PASTE(x, y) SWIFT_PASTE_HELPER(x, y)
#endif
#if !defined(SWIFT_METATYPE)
# define SWIFT_METATYPE(X) Class
#endif
#if !defined(SWIFT_CLASS_PROPERTY)
# if __has_feature(objc_class_property)
#  define SWIFT_CLASS_PROPERTY(...) __VA_ARGS__
# else
#  define SWIFT_CLASS_PROPERTY(...) 
# endif
#endif
#if !defined(SWIFT_RUNTIME_NAME)
# if __has_attribute(objc_runtime_name)
#  define SWIFT_RUNTIME_NAME(X) __attribute__((objc_runtime_name(X)))
# else
#  define SWIFT_RUNTIME_NAME(X) 
# endif
#endif
#if !defined(SWIFT_COMPILE_NAME)
# if __has_attribute(swift_name)
#  define SWIFT_COMPILE_NAME(X) __attribute__((swift_name(X)))
# else
#  define SWIFT_COMPILE_NAME(X) 
# endif
#endif
#if !defined(SWIFT_METHOD_FAMILY)
# if __has_attribute(objc_method_family)
#  define SWIFT_METHOD_FAMILY(X) __attribute__((objc_method_family(X)))
# else
#  define SWIFT_METHOD_FAMILY(X) 
# endif
#endif
#if !defined(SWIFT_NOESCAPE)
# if __has_attribute(noescape)
#  define SWIFT_NOESCAPE __attribute__((noescape))
# else
#  define SWIFT_NOESCAPE 
# endif
#endif
#if !defined(SWIFT_RELEASES_ARGUMENT)
# if __has_attribute(ns_consumed)
#  define SWIFT_RELEASES_ARGUMENT __attribute__((ns_consumed))
# else
#  define SWIFT_RELEASES_ARGUMENT 
# endif
#endif
#if !defined(SWIFT_WARN_UNUSED_RESULT)
# if __has_attribute(warn_unused_result)
#  define SWIFT_WARN_UNUSED_RESULT __attribute__((warn_unused_result))
# else
#  define SWIFT_WARN_UNUSED_RESULT 
# endif
#endif
#if !defined(SWIFT_NORETURN)
# if __has_attribute(noreturn)
#  define SWIFT_NORETURN __attribute__((noreturn))
# else
#  define SWIFT_NORETURN 
# endif
#endif
#if !defined(SWIFT_CLASS_EXTRA)
# define SWIFT_CLASS_EXTRA 
#endif
#if !defined(SWIFT_PROTOCOL_EXTRA)
# define SWIFT_PROTOCOL_EXTRA 
#endif
#if !defined(SWIFT_ENUM_EXTRA)
# define SWIFT_ENUM_EXTRA 
#endif
#if !defined(SWIFT_CLASS)
# if __has_attribute(objc_subclassing_restricted)
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_subclassing_restricted)) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# else
#  define SWIFT_CLASS(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
#  define SWIFT_CLASS_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_CLASS_EXTRA
# endif
#endif
#if !defined(SWIFT_RESILIENT_CLASS)
# if __has_attribute(objc_class_stub)
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME) __attribute__((objc_class_stub))
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) __attribute__((objc_class_stub)) SWIFT_CLASS_NAMED(SWIFT_NAME)
# else
#  define SWIFT_RESILIENT_CLASS(SWIFT_NAME) SWIFT_CLASS(SWIFT_NAME)
#  define SWIFT_RESILIENT_CLASS_NAMED(SWIFT_NAME) SWIFT_CLASS_NAMED(SWIFT_NAME)
# endif
#endif
#if !defined(SWIFT_PROTOCOL)
# define SWIFT_PROTOCOL(SWIFT_NAME) SWIFT_RUNTIME_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
# define SWIFT_PROTOCOL_NAMED(SWIFT_NAME) SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_PROTOCOL_EXTRA
#endif
#if !defined(SWIFT_EXTENSION)
# define SWIFT_EXTENSION(M) SWIFT_PASTE(M##_Swift_, __LINE__)
#endif
#if !defined(OBJC_DESIGNATED_INITIALIZER)
# if __has_attribute(objc_designated_initializer)
#  define OBJC_DESIGNATED_INITIALIZER __attribute__((objc_designated_initializer))
# else
#  define OBJC_DESIGNATED_INITIALIZER 
# endif
#endif
#if !defined(SWIFT_ENUM_ATTR)
# if __has_attribute(enum_extensibility)
#  define SWIFT_ENUM_ATTR(_extensibility) __attribute__((enum_extensibility(_extensibility)))
# else
#  define SWIFT_ENUM_ATTR(_extensibility) 
# endif
#endif
#if !defined(SWIFT_ENUM)
# define SWIFT_ENUM(_type, _name, _extensibility) enum _name : _type _name; enum SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# if __has_feature(generalized_swift_name)
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) enum _name : _type _name SWIFT_COMPILE_NAME(SWIFT_NAME); enum SWIFT_COMPILE_NAME(SWIFT_NAME) SWIFT_ENUM_ATTR(_extensibility) SWIFT_ENUM_EXTRA _name : _type
# else
#  define SWIFT_ENUM_NAMED(_type, _name, SWIFT_NAME, _extensibility) SWIFT_ENUM(_type, _name, _extensibility)
# endif
#endif
#if !defined(SWIFT_UNAVAILABLE)
# define SWIFT_UNAVAILABLE __attribute__((unavailable))
#endif
#if !defined(SWIFT_UNAVAILABLE_MSG)
# define SWIFT_UNAVAILABLE_MSG(msg) __attribute__((unavailable(msg)))
#endif
#if !defined(SWIFT_AVAILABILITY)
# define SWIFT_AVAILABILITY(plat, ...) __attribute__((availability(plat, __VA_ARGS__)))
#endif
#if !defined(SWIFT_WEAK_IMPORT)
# define SWIFT_WEAK_IMPORT __attribute__((weak_import))
#endif
#if !defined(SWIFT_DEPRECATED)
# define SWIFT_DEPRECATED __attribute__((deprecated))
#endif
#if !defined(SWIFT_DEPRECATED_MSG)
# define SWIFT_DEPRECATED_MSG(...) __attribute__((deprecated(__VA_ARGS__)))
#endif
#if !defined(SWIFT_DEPRECATED_OBJC)
# if __has_feature(attribute_diagnose_if_objc)
#  define SWIFT_DEPRECATED_OBJC(Msg) __attribute__((diagnose_if(1, Msg, "warning")))
# else
#  define SWIFT_DEPRECATED_OBJC(Msg) SWIFT_DEPRECATED_MSG(Msg)
# endif
#endif
#if defined(__OBJC__)
#if !defined(IBSegueAction)
# define IBSegueAction 
#endif
#endif
#if !defined(SWIFT_EXTERN)
# if defined(__cplusplus)
#  define SWIFT_EXTERN extern "C"
# else
#  define SWIFT_EXTERN extern
# endif
#endif
#if !defined(SWIFT_CALL)
# define SWIFT_CALL __attribute__((swiftcall))
#endif
#if !defined(SWIFT_INDIRECT_RESULT)
# define SWIFT_INDIRECT_RESULT __attribute__((swift_indirect_result))
#endif
#if !defined(SWIFT_CONTEXT)
# define SWIFT_CONTEXT __attribute__((swift_context))
#endif
#if !defined(SWIFT_ERROR_RESULT)
# define SWIFT_ERROR_RESULT __attribute__((swift_error_result))
#endif
#if defined(__cplusplus)
# define SWIFT_NOEXCEPT noexcept
#else
# define SWIFT_NOEXCEPT 
#endif
#if !defined(SWIFT_C_INLINE_THUNK)
# if __has_attribute(always_inline)
# if __has_attribute(nodebug)
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline)) __attribute__((nodebug))
# else
#  define SWIFT_C_INLINE_THUNK inline __attribute__((always_inline))
# endif
# else
#  define SWIFT_C_INLINE_THUNK inline
# endif
#endif
#if defined(_WIN32)
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL __declspec(dllimport)
#endif
#else
#if !defined(SWIFT_IMPORT_STDLIB_SYMBOL)
# define SWIFT_IMPORT_STDLIB_SYMBOL 
#endif
#endif
#if defined(__OBJC__)
#if __has_feature(objc_modules)
#if __has_warning("-Watimport-in-framework-header")
#pragma clang diagnostic ignored "-Watimport-in-framework-header"
#endif
@import ObjectiveC;
#endif

#endif
#pragma clang diagnostic ignored "-Wproperty-attribute-mismatch"
#pragma clang diagnostic ignored "-Wduplicate-method-arg"
#if __has_warning("-Wpragma-clang-attribute")
# pragma clang diagnostic ignored "-Wpragma-clang-attribute"
#endif
#pragma clang diagnostic ignored "-Wunknown-pragmas"
#pragma clang diagnostic ignored "-Wnullability"
#pragma clang diagnostic ignored "-Wdollar-in-identifier-extension"
#pragma clang diagnostic ignored "-Wunsafe-buffer-usage"

#if __has_attribute(external_source_symbol)
# pragma push_macro("any")
# undef any
# pragma clang attribute push(__attribute__((external_source_symbol(language="Swift", defined_in="AzureCommunicationCommon",generated_declaration))), apply_to=any(function,enum,objc_interface,objc_category,objc_protocol))
# pragma pop_macro("any")
#endif

#if defined(__OBJC__)

@class NSString;
@class NSDate;
/// Represents an Azure service bearer access token with expiry information.
SWIFT_CLASS("_TtC24AzureCommunicationCommon24CommunicationAccessToken")
@interface CommunicationAccessToken : NSObject
@property (nonatomic, readonly, copy) NSString * _Nonnull token;
@property (nonatomic, readonly, copy) NSDate * _Nonnull expiresOn;
/// Creates a new instance of CommunicationAccessToken using the provided <code>token</code> and <code>expiresOn</code>.
/// \param token The bearer access token value
///
/// \param expiresOn The bearer access token expiry date.
///
- (nonnull instancetype)initWithToken:(NSString * _Nonnull)token expiresOn:(NSDate * _Nonnull)expiresOn OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

SWIFT_CLASS("_TtC24AzureCommunicationCommon29CommunicationCloudEnvironment")
@interface CommunicationCloudEnvironment : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CommunicationCloudEnvironment * _Nonnull Public;)
+ (CommunicationCloudEnvironment * _Nonnull)Public SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CommunicationCloudEnvironment * _Nonnull Dod;)
+ (CommunicationCloudEnvironment * _Nonnull)Dod SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) CommunicationCloudEnvironment * _Nonnull Gcch;)
+ (CommunicationCloudEnvironment * _Nonnull)Gcch SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithEnvironmentValue:(NSString * _Nonnull)environmentValue OBJC_DESIGNATED_INITIALIZER;
- (NSString * _Nonnull)getEnvironmentValue SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

@class IdentifierKind;
/// Common Communication Identifier protocol for all Azure Communication Services.
/// All Communication Identifiers conform to this protocol.
SWIFT_PROTOCOL("_TtP24AzureCommunicationCommon23CommunicationIdentifier_")
@protocol CommunicationIdentifier <NSObject>
@property (nonatomic, readonly, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
@end

@class CommunicationTokenRefreshOptions;
/// The Azure Communication Services User token credential. This class is used to cache/refresh the access token required by Azure Communication Services.
SWIFT_CLASS("_TtC24AzureCommunicationCommon28CommunicationTokenCredential")
@interface CommunicationTokenCredential : NSObject
/// Creates a static <code>CommunicationTokenCredential</code> object from the provided token.
/// \param token The static token to use for authenticating all requests.
///
///
/// throws:
/// <code>NSError</code> if the provided token is not a valid user token. <code>userInfo</code> contains <code>message</code> key for reason.
- (nullable instancetype)initWithToken:(NSString * _Nonnull)token error:(NSError * _Nullable * _Nullable)error OBJC_DESIGNATED_INITIALIZER;
/// Creates a CommunicationTokenCredential that automatically refreshes the token.
/// \param options Options for how the token will be refreshed
///
///
/// throws:
/// <code>NSError</code> if the provided token is not a valid user token. <code>userInfo</code> contains <code>message</code> key for reason.
- (nullable instancetype)initWithOptions:(CommunicationTokenRefreshOptions * _Nonnull)options error:(NSError * _Nullable * _Nullable)error OBJC_DESIGNATED_INITIALIZER;
/// Retrieve an access token from the credential.
/// \param completionHandler Closure that accepts an optional <code>AccessToken</code> or optional <code>Error</code> as parameters.
/// <code>AccessToken</code> returns a token and an expiry date if applicable. <code>Error</code> returns <code>nil</code> if the current token can be returned.
///
- (void)tokenWithCompletionHandler:(void (^ _Nonnull)(CommunicationAccessToken * _Nullable, NSError * _Nullable))completionHandler;
/// Disposes the CommunicationTokenCredential and cancels any internal auto-refresh operation.
- (void)cancel;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// The Communication Token Refresh Options. Used to initialize a <code>CommunicationTokenCredential</code>
/// seealso:
/// <code> CommunicationTokenCredential.token(...)</code>
SWIFT_CLASS("_TtC24AzureCommunicationCommon32CommunicationTokenRefreshOptions")
@interface CommunicationTokenRefreshOptions : NSObject
/// Initializes a new instance of <code>CommunicationTokenRefreshOptions</code>
/// The cached token is updated if <code>token(completionHandler: )</code> is called and if the difference between the current time and token expiry time is less than 120s.
/// If <code>refreshProactively</code> parameter  is <code>true</code>:
/// <ul>
///   <li>
///     The cached token will be updated in the background when the difference between the current time and token expiry time is less than 600s.
///   </li>
///   <li>
///     The cached token will be updated immediately when the constructor is invoked and <code>initialToken</code> is expired
///   </li>
///   <li>
///     Parameters:
///   </li>
///   <li>
///     initialToken: The initial value of the token.
///   </li>
///   <li>
///     refreshProactively: Whether the token should be proactively refreshed in the background.
///   </li>
///   <li>
///     tokenRefresher: Closure to call when a new token value is needed.
///   </li>
/// </ul>
- (nonnull instancetype)initWithInitialToken:(NSString * _Nullable)initialToken refreshProactively:(BOOL)refreshProactively tokenRefresher:(void (^ _Nonnull)(void (^ _Nonnull)(NSString * _Nullable, NSError * _Nullable)))tokenRefresher OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Communication identifier for Communication Services Users
SWIFT_CLASS("_TtC24AzureCommunicationCommon27CommunicationUserIdentifier")
@interface CommunicationUserIdentifier : NSObject <CommunicationIdentifier>
@property (nonatomic, readonly, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
@property (nonatomic, readonly, copy) NSString * _Nonnull identifier;
/// Creates a CommunicationUserIdentifier object
/// \param identifier identifier representing the object identity
///
- (nonnull instancetype)initWithIdentifier:(NSString * _Nonnull)identifier OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// The IdentifierKind for a given CommunicationIdentifier.
SWIFT_CLASS("_TtC24AzureCommunicationCommon14IdentifierKind")
@interface IdentifierKind : NSObject
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) IdentifierKind * _Nonnull communicationUser;)
+ (IdentifierKind * _Nonnull)communicationUser SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) IdentifierKind * _Nonnull phoneNumber;)
+ (IdentifierKind * _Nonnull)phoneNumber SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) IdentifierKind * _Nonnull microsoftTeamsUser;)
+ (IdentifierKind * _Nonnull)microsoftTeamsUser SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) IdentifierKind * _Nonnull microsoftTeamsApp;)
+ (IdentifierKind * _Nonnull)microsoftTeamsApp SWIFT_WARN_UNUSED_RESULT;
SWIFT_CLASS_PROPERTY(@property (nonatomic, class, readonly, strong) IdentifierKind * _Nonnull unknown;)
+ (IdentifierKind * _Nonnull)unknown SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)initWithRawValue:(NSString * _Nonnull)rawValue OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Communication identifier for Microsoft Teams applications.
SWIFT_CLASS("_TtC24AzureCommunicationCommon27MicrosoftTeamsAppIdentifier")
@interface MicrosoftTeamsAppIdentifier : NSObject <CommunicationIdentifier>
@property (nonatomic, readonly, copy) NSString * _Nonnull appId;
@property (nonatomic, readonly, strong) CommunicationCloudEnvironment * _Nonnull cloudEnvironment;
@property (nonatomic, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
/// Creates a MicrosoftTeamsAppIdentifier object
/// \param appId The id of the Microsoft Teams application.
///
/// \param cloudEnvironment The cloud that the Microsoft Teams application belongs to.
/// A null value translates to the Public cloud.
///
- (nonnull instancetype)initWithAppId:(NSString * _Nonnull)appId cloudEnvironment:(CommunicationCloudEnvironment * _Nonnull)cloudEnvironment OBJC_DESIGNATED_INITIALIZER;
/// Returns a Boolean value that indicates whether the receiver is equal to another given object.
/// This will automatically return false if object being compared to is not a MicrosoftTeamsAppIdentifier.
/// \param object The object with which to compare the receiver. 
///
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Communication identifier for Microsoft Teams Users
SWIFT_CLASS("_TtC24AzureCommunicationCommon28MicrosoftTeamsUserIdentifier")
@interface MicrosoftTeamsUserIdentifier : NSObject <CommunicationIdentifier>
@property (nonatomic, readonly, copy) NSString * _Nonnull userId;
@property (nonatomic, readonly) BOOL isAnonymous;
@property (nonatomic, readonly, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
@property (nonatomic, readonly, strong) CommunicationCloudEnvironment * _Nonnull cloudEnviroment SWIFT_DEPRECATED_MSG("", "cloudEnvironment");
@property (nonatomic, readonly, strong) CommunicationCloudEnvironment * _Nonnull cloudEnvironment;
/// Creates a MicrosoftTeamsUserIdentifier object
/// \param userId Id of the Microsoft Teams user. If the user isn’t anonymous,
/// the id is the AAD object id of the user.
///
/// \param isAnonymous Set this to true if the user is anonymous:
/// for example when joining a meeting with a share link.
///
/// \param rawId The optional raw id of the Microsoft Teams User identifier.
///
/// \param cloudEnvironment The cloud that the Microsoft Team user belongs to.
/// A null value translates to the Public cloud.
///
- (nonnull instancetype)initWithUserId:(NSString * _Nonnull)userId isAnonymous:(BOOL)isAnonymous rawId:(NSString * _Nullable)rawId cloudEnvironment:(CommunicationCloudEnvironment * _Nonnull)cloudEnvironment OBJC_DESIGNATED_INITIALIZER;
/// Returns a Boolean value that indicates whether the receiver is equal to another given object.
/// This will automatically return false if object being compared to is not a MicrosoftTeamsUserIdentifier.
/// \param object The object with which to compare the receiver. 
///
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Communication identifier for Communication Services representing a Phone Number
SWIFT_CLASS("_TtC24AzureCommunicationCommon21PhoneNumberIdentifier")
@interface PhoneNumberIdentifier : NSObject <CommunicationIdentifier>
@property (nonatomic, readonly, copy) NSString * _Nonnull phoneNumber;
@property (nonatomic, readonly, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
/// Creates a PhoneNumberIdentifier object
/// \param phoneNumber phone number to create the object, different from identifier
///
/// \param rawId The optional raw id of the phone number.
///
- (nonnull instancetype)initWithPhoneNumber:(NSString * _Nonnull)phoneNumber rawId:(NSString * _Nullable)rawId OBJC_DESIGNATED_INITIALIZER;
/// Returns a Boolean value that indicates whether the receiver is equal to another given object.
/// This will automatically return false if object being compared to is not a PhoneNumberIdentifier.
/// \param object The object with which to compare the receiver. 
///
- (BOOL)isEqual:(id _Nullable)object SWIFT_WARN_UNUSED_RESULT;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

/// Catch-all for all other Communication identifiers for Communication Services
/// It is not advisable to rely on this type of identifier, as UnknownIdentifier could become a new or existing distinct type in the future.
SWIFT_CLASS("_TtC24AzureCommunicationCommon17UnknownIdentifier")
@interface UnknownIdentifier : NSObject <CommunicationIdentifier>
@property (nonatomic, readonly, copy) NSString * _Nonnull rawId;
@property (nonatomic, readonly, strong) IdentifierKind * _Nonnull kind;
@property (nonatomic, readonly, copy) NSString * _Nonnull identifier;
/// Creates a UnknownIdentifier object
/// \param identifier identifier representing the object identity
///
- (nonnull instancetype)initWithIdentifier:(NSString * _Nonnull)identifier OBJC_DESIGNATED_INITIALIZER;
- (nonnull instancetype)init SWIFT_UNAVAILABLE;
+ (nonnull instancetype)new SWIFT_UNAVAILABLE_MSG("-init is unavailable");
@end

#endif
#if __has_attribute(external_source_symbol)
# pragma clang attribute pop
#endif
#if defined(__cplusplus)
#endif
#pragma clang diagnostic pop
#endif

#else
#error unsupported Swift architecture
#endif
