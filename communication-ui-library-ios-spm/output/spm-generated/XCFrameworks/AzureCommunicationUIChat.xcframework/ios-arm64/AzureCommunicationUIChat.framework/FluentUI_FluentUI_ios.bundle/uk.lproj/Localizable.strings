//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Виконується";
"Accessibility.ActivityIndicator.Stopped.label" = "Виконання припинено";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Оповіщення";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Закрити";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Торкніться двічі, щоб закрити";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Готово";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Торкніться двічі, щоб перемкнути виділення ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Календар";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Виберіть дату";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "Подій: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "немає подій";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Місяць";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "День";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Рік";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Дата";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Година";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Хвилина";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "д. п./п. п.";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Тиждень місяця";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "День тижня";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "перш.";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "друг.";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "трет.";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "четв.";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "останн.";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Розгорнути";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Торкніться двічі, щоб розгорнути";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Згорнути";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Торкніться двічі, щоб згорнути";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Готово";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Помилка";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Завантаження";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d з %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Профіль облікового запису";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d із %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Торкніться двічі, щоб переглянути більше дій";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Інші дії";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Двічі торкніться, щоб перемкнути параметр";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Увімкнуто";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Вимкнуто";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, елементів: %@";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, непрочитане";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Додатково";

/* Generic label for cancel action */
"Common.Cancel" = "Скасувати";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Спільні";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Особисті";

/* Just now date string */
"Date.Now" = "Щойно";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm тому";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh тому";

/* Yesterday string */
"Date.Yesterday" = "Учора";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Вчора в %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@, %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Пошук у каталозі";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Час початку";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Час завершення";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Дата початку";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Дата завершення";

/* Presence - Available status */
"Presence.Available" = "Доступний";

/* Presence - Away status */
"Presence.Away" = "Не на місці";

/* Presence - Busy status */
"Presence.Busy" = "Зайнятий";

/* Presence - Do not disturb status */
"Presence.DND" = "Не турбувати";

/* Presence - Out of office status */
"Presence.OOF" = "Не на робочому місці";

/* Presence - Offline status */
"Presence.Offline" = "Офлайн";

/* Presence - Unknown status */
"Presence.Unknown" = "Невідомо";

/* Presence - Blocked status */
"Presence.Blocked" = "Заблокований";
