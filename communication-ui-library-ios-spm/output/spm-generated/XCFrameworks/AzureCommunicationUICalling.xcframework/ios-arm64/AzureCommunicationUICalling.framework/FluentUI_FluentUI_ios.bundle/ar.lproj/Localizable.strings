//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "قيد التقدم";
"Accessibility.ActivityIndicator.Stopped.label" = "تم إيقاف التقدم";

/* Accessibility alert for common use */
"Accessibility.Alert" = "تنبيه";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "تجاهل";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "اضغط ضغطاً مزدوجاً للتجاهل";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "تم";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "اضغط ضغطاً مزدوجاً برفق لتبديل التحديد";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "التقويم";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "تحديد تاريخ";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d من الأحداث";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "لا توجد أحداث";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "الشهر";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "اليوم";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "السنة";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "التاريخ";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "ساعة";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "دقيقة";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "ص/م";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "أسبوع الشهر";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "اليوم من الأسبوع";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "الأول";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "الثاني";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "الثالث";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "الرابع";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "الأخير";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "توسيع";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "اضغط ضغطاً مزدوجاً برفق للتوسيع";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "طي";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "اضغط ضغطاً مزدوجاً برفق للطي";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "تم";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "فشل";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "يتم الآن التحميل";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d من %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "ملف تعريف الحساب";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@، %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d من %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "اضغط ضغطاً مزدوجاً لعرض مزيد من الإجراءات";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "مزيد من الإجراءات";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "اضغط ضغطاً مزدوجاً برفق لتبديل الإعداد";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "تشغيل";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "إيقاف التشغيل";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@، %@ من العناصر";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, غير مقروء";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "المزيد";

/* Generic label for cancel action */
"Common.Cancel" = "إلغاء";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "مشترك";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "أنا فقط";

/* Just now date string */
"Date.Now" = "للتو";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "منذ %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "منذ %ldh";

/* Yesterday string */
"Date.Yesterday" = "أمس";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "أمس الساعة %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ الساعة %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "البحث في الدليل";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "وقت البدء";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "وقت الانتهاء";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "تاريخ البدء";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "تاريخ الانتهاء";

/* Presence - Available status */
"Presence.Available" = "متوفر";

/* Presence - Away status */
"Presence.Away" = "بالخارج";

/* Presence - Busy status */
"Presence.Busy" = "مشغول";

/* Presence - Do not disturb status */
"Presence.DND" = "ممنوع الإزعاج";

/* Presence - Out of office status */
"Presence.OOF" = "خارج المكتب";

/* Presence - Offline status */
"Presence.Offline" = "دون اتصال";

/* Presence - Unknown status */
"Presence.Unknown" = "غير معروف";

/* Presence - Blocked status */
"Presence.Blocked" = "ممنوعة";
