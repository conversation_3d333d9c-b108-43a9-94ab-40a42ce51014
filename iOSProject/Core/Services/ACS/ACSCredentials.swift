//
//  ACSCredentials.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation

// MARK: - ACS Credentials Management

/// Secure credential management for Azure Communication Services
struct ACSCredentials {
    
    // MARK: - Environment-based Configuration
    
    /// Current build environment
    enum Environment: String, CaseIterable {
        case development = "Development"
        case staging = "Staging"
        case production = "Production"
        
        var isProduction: Bool {
            return self == .production
        }
    }
    
    // MARK: - Credential Sources
    
    /// Sources for ACS credentials in order of priority
    enum CredentialSource {
        case environmentVariables    // Highest priority
        case infoPlist              // Medium priority
        case bundleConfiguration    // Lowest priority
        case hardcoded             // Development only
    }
    
    // MARK: - Properties
    
    let connectionString: String
    let endpoint: String
    let accessKey: String
    let resourceName: String
    let environment: Environment
    
    // MARK: - Initialization
    
    /// Initialize with automatic credential detection
    init() throws {
        self.environment = Self.detectEnvironment()
        
        // Try to load credentials from various sources
        if let credentials = Self.loadFromEnvironmentVariables() {
            self.connectionString = credentials.connectionString
            self.endpoint = credentials.endpoint
            self.accessKey = credentials.accessKey
            self.resourceName = credentials.resourceName
        } else if let credentials = Self.loadFromInfoPlist() {
            self.connectionString = credentials.connectionString
            self.endpoint = credentials.endpoint
            self.accessKey = credentials.accessKey
            self.resourceName = credentials.resourceName
        } else if let credentials = Self.loadFromBundleConfiguration() {
            self.connectionString = credentials.connectionString
            self.endpoint = credentials.endpoint
            self.accessKey = credentials.accessKey
            self.resourceName = credentials.resourceName
        } else if !environment.isProduction {
            // Use development credentials only in non-production
            let devCredentials = Self.developmentCredentials()
            self.connectionString = devCredentials.connectionString
            self.endpoint = devCredentials.endpoint
            self.accessKey = devCredentials.accessKey
            self.resourceName = devCredentials.resourceName
        } else {
            throw ACSError.configurationInvalid
        }
        
        // Validate credentials
        try validateCredentials()
    }
    
    /// Initialize with explicit credentials (for testing)
    init(connectionString: String, endpoint: String, accessKey: String, resourceName: String, environment: Environment = .development) throws {
        self.connectionString = connectionString
        self.endpoint = endpoint
        self.accessKey = accessKey
        self.resourceName = resourceName
        self.environment = environment
        
        try validateCredentials()
    }
    
    // MARK: - Credential Loading Methods
    
    /// Load credentials from environment variables
    private static func loadFromEnvironmentVariables() -> (connectionString: String, endpoint: String, accessKey: String, resourceName: String)? {
        guard let connectionString = ProcessInfo.processInfo.environment["ACS_CONNECTION_STRING"],
              let endpoint = ProcessInfo.processInfo.environment["ACS_ENDPOINT"],
              let accessKey = ProcessInfo.processInfo.environment["ACS_ACCESS_KEY"],
              let resourceName = ProcessInfo.processInfo.environment["ACS_RESOURCE_NAME"] else {
            return nil
        }
        
        return (connectionString, endpoint, accessKey, resourceName)
    }
    
    /// Load credentials from Info.plist
    private static func loadFromInfoPlist() -> (connectionString: String, endpoint: String, accessKey: String, resourceName: String)? {
        guard let path = Bundle.main.path(forResource: "Info", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let connectionString = plist["ACSConnectionString"] as? String,
              let endpoint = plist["ACSEndpoint"] as? String,
              let accessKey = plist["ACSAccessKey"] as? String,
              let resourceName = plist["ACSResourceName"] as? String else {
            return nil
        }
        
        return (connectionString, endpoint, accessKey, resourceName)
    }
    
    /// Load credentials from bundle configuration file
    private static func loadFromBundleConfiguration() -> (connectionString: String, endpoint: String, accessKey: String, resourceName: String)? {
        guard let path = Bundle.main.path(forResource: "ACSConfig", ofType: "plist"),
              let plist = NSDictionary(contentsOfFile: path),
              let connectionString = plist["ConnectionString"] as? String,
              let endpoint = plist["Endpoint"] as? String,
              let accessKey = plist["AccessKey"] as? String,
              let resourceName = plist["ResourceName"] as? String else {
            return nil
        }
        
        return (connectionString, endpoint, accessKey, resourceName)
    }
    
    /// Development credentials (never use in production)
    private static func developmentCredentials() -> (connectionString: String, endpoint: String, accessKey: String, resourceName: String) {
        return (
            connectionString: "endpoint=https://your-dev-acs-resource.communication.azure.com/;accesskey=your-dev-access-key",
            endpoint: "https://your-dev-acs-resource.communication.azure.com/",
            accessKey: "your-dev-access-key",
            resourceName: "your-dev-acs-resource"
        )
    }
    
    // MARK: - Environment Detection
    
    /// Detect current environment based on build configuration
    private static func detectEnvironment() -> Environment {
        #if DEBUG
            return .development
        #elseif STAGING
            return .staging
        #else
            return .production
        #endif
    }
    
    // MARK: - Validation
    
    /// Validate that credentials are properly formatted
    private func validateCredentials() throws {
        // Validate connection string format
        guard connectionString.contains("endpoint=") && connectionString.contains("accesskey=") else {
            throw ACSError.configurationInvalid
        }
        
        // Validate endpoint URL
        guard URL(string: endpoint) != nil else {
            throw ACSError.configurationInvalid
        }
        
        // Validate access key is not empty
        guard !accessKey.isEmpty else {
            throw ACSError.configurationInvalid
        }
        
        // Validate resource name is not empty
        guard !resourceName.isEmpty else {
            throw ACSError.configurationInvalid
        }
        
        // Additional validation for production
        if environment.isProduction {
            guard !connectionString.contains("your-") && !endpoint.contains("your-") else {
                throw ACSError.configurationInvalid
            }
        }
    }
    
    // MARK: - Utility Methods
    
    /// Check if using mock/development credentials
    var isUsingMockCredentials: Bool {
        return connectionString.contains("your-") || endpoint.contains("your-")
    }
    
    /// Get sanitized description for logging (never log actual credentials)
    var description: String {
        return """
        ACS Credentials:
        - Environment: \(environment.rawValue)
        - Resource: \(resourceName)
        - Endpoint: \(endpoint.replacingOccurrences(of: accessKey, with: "***"))
        - Using Mock: \(isUsingMockCredentials)
        """
    }
}

// MARK: - ACS Credentials Extensions

extension ACSCredentials {
    
    /// Create ACSConfiguration from credentials
    func toConfiguration() -> ACSConfiguration {
        return ACSConfiguration(
            connectionString: connectionString,
            defaultDisplayName: AppConstants.ACS.defaultDisplayName,
            callTimeout: AppConstants.ACS.callTimeout
        )
    }
    
    /// Extract resource name from endpoint URL
    static func extractResourceName(from endpoint: String) -> String? {
        guard let url = URL(string: endpoint),
              let host = url.host else {
            return nil
        }
        
        // Extract resource name from hostname (e.g., "my-resource.communication.azure.com")
        let components = host.components(separatedBy: ".")
        return components.first
    }
}
