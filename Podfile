platform :ios, '16.0'
use_frameworks!

# Explicitly tell CocoaPods which workspace to use/create
workspace 'iOSProject'

target 'iOSProject' do
  # UI wrapper pod resolves compatible Azure dependencies
#  pod 'AzureCommunicationUICalling', '~> 1.14.2'

  # Do NOT pin transitive Azure pods unless required; removing mismatched pins
  # pod 'AzureCommunicationCalling', '2.15.0'
  # pod 'AzureCore', '1.0.0-beta.16'
  # pod 'AzureCommunicationCommon', '1.3.0'

  post_install do |installer|
    installer.pods_project.targets.each do |t|
      t.build_configurations.each do |c|
        c.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
        c.build_settings['SWIFT_STRICT_CONCURRENCY'] = 'minimal'
      end
    end
  end
end
