//
//  HomeHeaderSection.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

struct HomeHeaderSection: View {
    @Environment(\.appColors) var colors

    // MARK: - Properties
    let onACSButtonTapped: () -> Void

    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 60.h)

            HStack(spacing: 8.h) {
                // New Feature Badge
                HStack(spacing: 8.h) {
                    Text(AppConstants.Strings.newFeatureBadge)
                        .body12Medium()
                        .foregroundColor(colors.badgeText)
                        .padding(.horizontal, 12.h)
                        .padding(.vertical, 4.h)
                        .background(colors.surface)
                        .clipShape(RoundedRectangle(cornerRadius: 11.h))

                    Text(AppConstants.Strings.personalizedCoaching)
                        .body12Medium()
                        .foregroundColor(colors.primaryPurple)
                }

                Spacer()

                Image(ImageConstants.imgArrowrightDeepPurple300)
                    .resizable()
                    .frame(width: 16.h, height: 16.h)
                    .foregroundColor(colors.arrowIcon)
            }
            .padding(.horizontal, 16.h)
            .padding(.vertical, 8.h)
            .background(colors.badgeBackground)
            .clipShape(RoundedRectangle(cornerRadius: 15.h))
            .padding(.horizontal, 16.h)

            // ACS Communication Button
            Button(action: onACSButtonTapped) {
                HStack(spacing: 8.h) {
                    Image(systemName: "video.fill")
                        .resizable()
                        .frame(width: 16.h, height: 12.h)
                        .foregroundColor(colors.primaryPurple)

                    Text(AppConstants.ACS.UI.communicationButtonTitle)
                        .body14Medium()
                        .foregroundColor(colors.primaryText)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .resizable()
                        .frame(width: 8.h, height: 12.h)
                        .foregroundColor(colors.arrowIcon)
                }
                .padding(.horizontal, 16.h)
                .padding(.vertical, 12.h)
                .background(colors.surface)
                .clipShape(RoundedRectangle(cornerRadius: 12.h))
            }
            .padding(.horizontal, 16.h)
            .padding(.top, 32.h)
        }
        .padding(.vertical, 20.h)
    }
}

#Preview {
    HomeHeaderSection(onACSButtonTapped: {
        print("ACS Button tapped")
    })
    .attachAllEnvironmentObjects()
}
