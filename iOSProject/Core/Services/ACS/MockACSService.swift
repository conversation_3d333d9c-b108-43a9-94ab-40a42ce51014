//
//  MockACSService.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation

// MARK: - Mock ACS Service for Development

/// Mock implementation of ACS service for development and testing when Azure SDK is not available
class MockACSService: ACSServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    @Published private(set) var callState: ACSCallState = .idle
    
    private let configuration: ACSConfiguration
    private let tokenProvider: ACSTokenProviderProtocol
    
    // Mock properties for simulation
    private var isSimulatingCall = false
    private var simulatedCallId: String?

    // Test properties
    var shouldFailCredentialValidation = false
    
    // MARK: - Initialization
    
    init(configuration: ACSConfiguration, tokenProvider: ACSTokenProviderProtocol) {
        self.configuration = configuration
        self.tokenProvider = tokenProvider
    }
    
    // MARK: - ACS Service Protocol Implementation
    
    func startCall(with configuration: ACSCallConfiguration, from viewController: UIViewController? = nil) async throws {
        try await simulateCallFlow {
            await MainActor.run {
                self.callState = .connecting
            }
            
            // Simulate connection delay
            try await Task.sleep(nanoseconds: 2_000_000_000) // 2 seconds
            
            await MainActor.run {
                self.callState = .connected
                self.isSimulatingCall = true
                self.simulatedCallId = UUID().uuidString
            }
            
            // Show mock call interface
            await self.showMockCallInterface(for: configuration)
        }
    }
    
    func joinTeamsMeeting(url: String, displayName: String? = nil, from viewController: UIViewController? = nil) async throws {
        let config = ACSCallConfiguration.teamsMeeting(url: url, displayName: displayName)
        try await startCall(with: config, from: viewController)
    }
    
    func createGroupCall(displayName: String? = nil, from viewController: UIViewController? = nil) async throws -> String {
        let groupId = UUID().uuidString
        let config = ACSCallConfiguration.groupCall(groupId: groupId, displayName: displayName)
        try await startCall(with: config, from: viewController)
        return groupId
    }
    
    func endCall() async throws {
        await MainActor.run {
            callState = .disconnecting
        }
        
        // Simulate disconnection delay
        try await Task.sleep(nanoseconds: 1_000_000_000) // 1 second
        
        await MainActor.run {
            callState = .disconnected
            isSimulatingCall = false
            simulatedCallId = nil
        }
    }
    
    func checkPermissions() async -> Bool {
        let cameraStatus = await checkCameraPermission()
        let microphoneStatus = await checkMicrophonePermission()
        return cameraStatus && microphoneStatus
    }

    func validateCredentials() -> ACSCredentialValidationResult {
        if shouldFailCredentialValidation {
            return .invalid(.usingMockCredentials)
        }
        return configuration.validateCredentials()
    }

    func requestPermissions() async throws {
        // Request camera permission
        let cameraStatus = await requestCameraPermission()
        
        // Request microphone permission
        let microphoneStatus = await requestMicrophonePermission()
        
        if !cameraStatus || !microphoneStatus {
            throw ACSError.permissionDenied("Camera and Microphone permissions are required")
        }
    }
    
    // MARK: - Private Methods
    
    private func simulateCallFlow(_ operation: @escaping () async throws -> Void) async throws {
        do {
            // Validate token
            let _ = try await tokenProvider.refreshTokenIfNeeded()
            
            // Check permissions
            let hasPermissions = await checkPermissions()
            if !hasPermissions {
                try await requestPermissions()
            }
            
            try await operation()
            
        } catch {
            await MainActor.run {
                if let acsError = error as? ACSError {
                    callState = .failed(acsError)
                } else {
                    callState = .failed(.callFailed(error.localizedDescription))
                }
            }
            throw error
        }
    }
    
    @MainActor
    private func showMockCallInterface(for configuration: ACSCallConfiguration) async {
        // In a real implementation, this would launch the Azure Communication Services UI
        // For now, we'll show a simple alert to simulate the call interface
        
        guard let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
              let window = windowScene.windows.first,
              let rootViewController = window.rootViewController else {
            return
        }
        
        let alert = UIAlertController(
            title: "Mock Communication Call",
            message: "This is a mock call interface.\nCall Type: \(getCallTypeDescription(for: configuration))\nDisplay Name: \(configuration.displayName)",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "End Call", style: .destructive) { _ in
            Task {
                try? await self.endCall()
            }
        })
        
        alert.addAction(UIAlertAction(title: "Continue Call", style: .default) { _ in
            // Keep the call running
        })
        
        rootViewController.present(alert, animated: true)
    }
    
    private func getCallTypeDescription(for configuration: ACSCallConfiguration) -> String {
        switch configuration {
        case .groupCall(let groupId, _):
            return "Group Call (ID: \(groupId.prefix(8))...)"
        case .teamsMeeting(let url, _):
            return "Teams Meeting (\(url.prefix(30))...)"
        case .oneToOneCall(let userId, _):
            return "One-to-One Call (User: \(userId.prefix(8))...)"
        }
    }
    
    // MARK: - Permission Helpers
    
    private func checkCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }
    
    private func checkMicrophonePermission() async -> Bool {
        let status = AVAudioSession.sharedInstance().recordPermission
        return status == .granted
    }
    
    private func requestCameraPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                continuation.resume(returning: granted)
            }
        }
    }
    
    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
}

// MARK: - Mock Call Composite Types
// These mock types are no longer needed since the real Azure SDK is now available
// They are kept here for reference and can be removed in a future cleanup
