//
//  FluentUI.swift
//  FluentUI Minimal Swift Module Interface
//
//  This provides minimal Swift module interface for FluentUI.
//  Actual implementation comes from embedded FluentUI in Azure XCFrameworks.
//

import Foundation

// Minimal Swift module interface - no implementation
// This satisfies "import FluentUI" statements without providing competing implementation

@objc public class FluentUIModule: NSObject {
    // Empty stub class to satisfy module requirements
    // All actual FluentUI functionality comes from embedded version in Azure XCFrameworks
}
