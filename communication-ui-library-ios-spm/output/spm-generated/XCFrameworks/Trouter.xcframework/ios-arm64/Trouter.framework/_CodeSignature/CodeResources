<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/SelfHostedTrouterClient.h</key>
		<data>
		CEI/zqN3f+UHlJ+UDklhV4sn4RU=
		</data>
		<key>Headers/Trouter.h</key>
		<data>
		xH7uRcnnB5bmyZkPfYnD3HHom/Y=
		</data>
		<key>Headers/TrouterSkypetokenProvider.h</key>
		<data>
		OmhvTqTwDyzAWOwzB8afDHYfy2g=
		</data>
		<key>Headers/TrouterUrlRegistrar.h</key>
		<data>
		19AjlpSsgFeg1Q9k9VNpK5Gswww=
		</data>
		<key>Info.plist</key>
		<data>
		E3qoJw8F6NPKfe7VAAj40cPdTcM=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		tj1rcu4vCm5p1F/vQi3wFrr/4Gc=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/SelfHostedTrouterClient.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Dr2Zev1VpublEPFGn7ADv79OifcIG+JVps33CJBgMAk=
			</data>
		</dict>
		<key>Headers/Trouter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XtCQBHpcYhpGPKheUlros3mt40MRahuAj5TTjiLPytY=
			</data>
		</dict>
		<key>Headers/TrouterSkypetokenProvider.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ii47JMbIiW6aTdNOjzruHwqLI9esmSfuMWWcE6X+YsI=
			</data>
		</dict>
		<key>Headers/TrouterUrlRegistrar.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ML5obRDceNok82E4fSynOd6f+/0Zc6sELaYt3Xnnjvw=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			aHrx0OhH32wWt9YSnF9V7DP9Swxb9HXE3sQM28UOY5I=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
