//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "În desfășurare";
"Accessibility.ActivityIndicator.Stopped.label" = "Progres oprit";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Avertizare";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Îndepărtare";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Atingeți de două ori pentru a îndepărta";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Gata";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Atingeți de două ori pentru a comuta selecția";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendar";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Selectați o dată";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d evenimente";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "niciun eveniment";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Lună";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Zi";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "An";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Dată";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Oră";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minut";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Săptămâna din lună";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Zi a săptămânii";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Prima";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "A doua";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "A treia";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "A patra";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Ultima";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Extindere";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Atingeți de două ori pentru a extinde";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Restrângere";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Atingeți de două ori pentru a restrânge";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Gata";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Nereușit";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Se încarcă";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d din %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil cont";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d din %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Atingeți de două ori pentru a vedea mai multe acțiuni";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Mai multe acțiuni";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Atingeți de două ori pentru a comuta setarea";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Activat";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Dezactivat";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elemente";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, necitit";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Mai multe";

/* Generic label for cancel action */
"Common.Cancel" = "Anulare";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Partajat";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Numai eu";

/* Just now date string */
"Date.Now" = "Chiar acum";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Acum %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Acum %ldh";

/* Yesterday string */
"Date.Yesterday" = "Ieri";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Ieri la %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ la %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Căutați în director";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Ora de început";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Ora de sfârșit";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Data de început";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Data de sfârșit";

/* Presence - Available status */
"Presence.Available" = "Disponibil";

/* Presence - Away status */
"Presence.Away" = "Plecat";

/* Presence - Busy status */
"Presence.Busy" = "Ocupat";

/* Presence - Do not disturb status */
"Presence.DND" = "Nu deranjați";

/* Presence - Out of office status */
"Presence.OOF" = "Absent de la birou";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Necunoscut";

/* Presence - Blocked status */
"Presence.Blocked" = "Blocat";
