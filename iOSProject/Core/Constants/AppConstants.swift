//
//  AppConstants.swift
//  iOSProject
//
//  Created by Code Quality Review on 10/08/2025.
//

import SwiftUI

/// Centralized constants for the iOS application
/// This file contains all hardcoded values that were previously scattered throughout the codebase
struct AppConstants {
    
    // MARK: - UserDefaults Keys
    struct UserDefaults {
        static let selectedTab = "selected_tab"
        static let isDarkMode = "isDarkMode"
        static let followSystemAppearance = "followSystemAppearance"
        static let userProfile = "user_profile"
        static let selectedLanguage = "selected_language"
    }
    
    // MARK: - Task Delays (in nanoseconds)
    struct Delays {
        /// Short delay for quick operations (100ms)
        static let short: UInt64 = 100_000_000
        
        /// Medium delay for standard operations (150ms)
        static let medium: UInt64 = 150_000_000
        
        /// Long delay for slower operations (200ms)
        static let long: UInt64 = 200_000_000
        
        /// Newsletter subscription delay (300ms)
        static let newsletter: UInt64 = 300_000_000
        
        /// User operation delay (500ms)
        static let userOperation: UInt64 = 500_000_000
    }
    
    // MARK: - User Interface Strings
    struct Strings {
        // MARK: - Common UI Elements
        static let error = "Error"
        static let ok = "OK"
        static let cancel = "Cancel"
        static let done = "Done"
        static let save = "Save"
        static let delete = "Delete"
        static let edit = "Edit"
        static let loading = "Loading..."
        
        // MARK: - Authentication
        static let login = "Login"
        static let logout = "Logout"
        static let signUp = "Sign Up"
        static let forgotPassword = "Forgot Password?"
        
        // MARK: - Validation Messages
        static let emailRequired = "Email is required"
        static let emailInvalid = "Please enter a valid email address"
        static let emailTooLong = "Email address is too long"
        static let passwordRequired = "Password is required"
        static let passwordTooShort = "Password must be at least 6 characters long"
        static let passwordTooLong = "Password is too long"
        static let passwordMustContainLetter = "Password must contain at least one letter"
        static let nameRequired = "Name is required"
        static let nameTooShort = "Name must be at least 2 characters long"
        static let nameTooLong = "Name is too long"
        static let nameInvalidCharacters = "Name can only contain letters, spaces, hyphens, and apostrophes"
        static let phoneRequired = "Phone number is required"
        static let phoneTooShort = "Phone number must be at least 10 digits"
        static let phoneTooLong = "Phone number is too long"
        
        // MARK: - Home Screen Content
        static let newFeatureBadge = "New"
        static let personalizedCoaching = "Personalized coaching in-app"
        static let portfolioPerformanceTitle:LocalizedStringKey = "Portfolio performance tracking made easy"
        static let portfolioPerformanceSubtitle:LocalizedStringKey = "Powerful, self-serve product and growth analytics to help you convert, engage, and retain more users. Trusted by over 4,000 startups."
        static let officialPartner = "Official partner of these companies"
        static let featuresTitle = "Features"
        static let unlockYourself = "Unlock yourself"
        static let featuresDescription = "Daily personalized fitness, sleep, and recovery data delivered to you in real time with Untitled. We're changing how you move."
        static let featuresSubtitle = "Daily personalized fitness, sleep, and recovery data delivered to you in real time with Untitled. We're changing how you move."
        static let startFreeTrial = "Start your free trial"
        static let personalPerformanceTracking = "Personal performance tracking made easy."
        static let pricingTitle = "Pricing"
        static let simpleTransparentPricing = "Simple, transparent pricing"
        static let pricingDescription = "We believe Untitled should be accessible to all companies, no matter the size."
        static let pricingSubtitle = "We believe Untitled should be accessible to all companies, no matter the size."
        static let stayUpToDate = "Stay up to date"
        static let stayUpdatedDescription = "Stay updated with the latest features and releases."
        static let newsletterTitle = "Stay up to date"
        static let newsletterSubtitle = "Stay updated with the latest features and releases."
        static let enterYourEmail = "Enter your email"
        static let emailPlaceholder = "Enter your email"
        static let subscribe = "Subscribe"
        static let privacyPolicyText = "We care about your data in our privacy policy."
        static let trustedByInvestors = "Trusted by 4,000+ investors"
        
        // MARK: - Placeholder Content
        static let heroImage = "Hero Image"
        static let featureMockup = "Feature Mockup"
        static let ctaVisual = "CTA Visual"
        
        // MARK: - Success Messages
        static let newsletterSubscriptionSuccess = "Successfully subscribed to newsletter"
        static let dataLoadSuccess = "Data loaded successfully"
        
        // MARK: - Error Messages
        static let dataLoadError = "Failed to load data"
        static let newsletterSubscriptionError = "Failed to subscribe"
        static let genericError = "An unexpected error occurred"
        static let networkError = "Network connection error"
        static let validEmailRequired = "Please enter a valid email address"
        
        // MARK: - Button Actions
        static let appStoreButtonTapped = "App Store button tapped"
        static let playStoreButtonTapped = "Play Store button tapped"
        static let getStartedTapped = "Get Started button tapped"
        static let pricingPlanSelected = "Pricing plan selected"
        
        // MARK: - System Messages
        static let languageChanged = "Language changed to"
        static let imageNotFound = "Image not found"
        static let usingPlaceholder = "using placeholder"
    }
    
    // MARK: - Numeric Constants
    struct Numbers {
        // MARK: - Validation Limits
        static let minPasswordLength = 6
        static let maxPasswordLength = 128
        static let maxEmailLength = 254
        static let minNameLength = 2
        static let maxNameLength = 50
        static let minPhoneDigits = 10
        static let maxPhoneDigits = 15
        
        // MARK: - UI Dimensions
        static let defaultSpacing: CGFloat = 16
        static let smallSpacing: CGFloat = 8
        static let largeSpacing: CGFloat = 24
        static let extraLargeSpacing: CGFloat = 32
        
        // MARK: - Animation Durations
        static let shortAnimationDuration: TimeInterval = 0.2
        static let mediumAnimationDuration: TimeInterval = 0.3
        static let longAnimationDuration: TimeInterval = 0.5
        
        // MARK: - Corner Radius
        static let smallCornerRadius: CGFloat = 8
        static let mediumCornerRadius: CGFloat = 12
        static let largeCornerRadius: CGFloat = 16
        
        // MARK: - Heights
        static let buttonHeight: CGFloat = 48
        static let inputFieldHeight: CGFloat = 44
        static let tabBarHeight: CGFloat = 80
    }
    
    // MARK: - System Icons
    struct SystemIcons {
        static let arrowRight = "arrow.right"
        static let iPhone = "iphone"
        static let appsIPhone = "apps.iphone"
        static let starFill = "star.fill"
        static let envelope = "envelope"
        static let house = "house"
        static let heart = "heart"
        static let bell = "bell"
        static let person = "person"
        static let gear = "gear"
    }
    
    // MARK: - Color Hex Values
    struct ColorHex {
        static let purple = "6840C6"
        static let darkPurple = "4C1D95"
        static let lightPurple = "A855F7"
        static let lightBackground = "F9F5FF"
        static let lightPurpleText = "E9D7FE"
    }
    
    // MARK: - Regular Expressions
    struct RegularExpressions {
        static let email = "^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$"
        static let password = ".*[A-Za-z]+.*"
        static let name = "^[A-Za-z\\s\\-']+$"
    }
    
    // MARK: - Network Configuration
    struct Network {
        static let timeoutInterval: TimeInterval = 30.0
        static let maxRetryAttempts = 3
    }
    
    // MARK: - Feature Flags
    struct FeatureFlags {
        static let enableDarkMode = true
        static let enableNotifications = true
        static let enableAnalytics = true
        static let enableCrashReporting = true
        static let enableAzureCommunicationServices = true
    }

    // MARK: - Azure Communication Services
    struct ACS {
        static let connectionString = "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key"
        static let defaultDisplayName = "Burhanudheen Rabbani"
        static let callTimeout: TimeInterval = 30.0

        struct Permissions {
            static let cameraUsage = "This app needs camera access to enable video calling through Azure Communication Services"
            static let microphoneUsage = "This app needs microphone access to enable voice calling through Azure Communication Services"
        }

        struct UI {
            static let communicationButtonTitle = "Start Communication"
            static let videoCallButtonTitle = "Video Call"
            static let voiceCallButtonTitle = "Voice Call"
            static let joinMeetingButtonTitle = "Join Meeting"
        }
    }
}

// MARK: - Convenience Extensions
extension AppConstants.Delays {
    /// Convert nanoseconds to TimeInterval (seconds)
    static func toTimeInterval(_ nanoseconds: UInt64) -> TimeInterval {
        return TimeInterval(nanoseconds) / 1_000_000_000
    }
}

extension AppConstants.Strings {
    /// Format error message with details
    static func errorWithDetails(_ error: Error) -> String {
        return "\(genericError): \(error.localizedDescription)"
    }
    
    /// Format data load error with specific details
    static func dataLoadErrorWithDetails(_ error: Error) -> String {
        return "\(dataLoadError): \(error.localizedDescription)"
    }
    
    /// Format newsletter subscription error with details
    static func newsletterErrorWithDetails(_ error: Error) -> String {
        return "\(newsletterSubscriptionError): \(error.localizedDescription)"
    }
}

// MARK: - Partner Logos
extension AppConstants {
    enum PartnerLogos: String, CaseIterable {
        case apple = "apple.logo"
        case microsoft = "microsoft.logo"
        case google = "google.logo"
        case amazon = "amazon.logo"
        case meta = "meta.logo"
        case netflix = "netflix.logo"

        var companyName: String {
            switch self {
            case .apple: return "Apple"
            case .microsoft: return "Microsoft"
            case .google: return "Google"
            case .amazon: return "Amazon"
            case .meta: return "Meta"
            case .netflix: return "Netflix"
            }
        }
    }
}
