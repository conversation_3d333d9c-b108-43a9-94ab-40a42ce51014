//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Folyamatban";
"Accessibility.ActivityIndicator.Stopped.label" = "Az előrehaladás leállt";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Értesítés";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Bezárás";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "<PERSON><PERSON><PERSON><PERSON> duplán a bezáráshoz";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Kész";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Koppintson duplán a kijelölés váltásához";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Naptár";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Dátum kiválasztása";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d esemény";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "nincs esemény";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Hónap";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Nap";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Év";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Dátum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Óra";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Perc";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "Délelőtt vagy délután";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Hónap hete";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Hét napja";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Első";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "második";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "harmadik";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "negyedik";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Utolsó";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Kibontás";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Koppintson duplán a kibontáshoz";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Összecsukás";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Koppintson duplán az összecsukásához";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Kész";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Sikertelen volt a feladat";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Betöltés folyamatban";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d., összesen %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Fiókprofil";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d/%2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Koppintson ide duplán a további műveletek megtekintéséhez";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "További műveletek";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Duplán koppintva bekapcsolhatja vagy kikapcsolhatja a beállítást";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Bekapcsolva";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Kikapcsolva";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elem";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, olvasatlan";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Továbbiak";

/* Generic label for cancel action */
"Common.Cancel" = "Mégse";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Megosztva";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Csak én";

/* Just now date string */
"Date.Now" = "Éppen most";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm telt el azóta";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh telt el azóta";

/* Yesterday string */
"Date.Yesterday" = "Tegnap";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Tegnap ekkor: %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ ekkor: %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Keresés a címtárban";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Kezdés ideje";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Befejezés ideje";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Kezdés dátuma";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Befejezés dátuma";

/* Presence - Available status */
"Presence.Available" = "Elérhető";

/* Presence - Away status */
"Presence.Away" = "Nincs a gépnél";

/* Presence - Busy status */
"Presence.Busy" = "Elfoglalt";

/* Presence - Do not disturb status */
"Presence.DND" = "Ne zavarjanak";

/* Presence - Out of office status */
"Presence.OOF" = "Házon kívül";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Ismeretlen";

/* Presence - Blocked status */
"Presence.Blocked" = "Letiltva";
