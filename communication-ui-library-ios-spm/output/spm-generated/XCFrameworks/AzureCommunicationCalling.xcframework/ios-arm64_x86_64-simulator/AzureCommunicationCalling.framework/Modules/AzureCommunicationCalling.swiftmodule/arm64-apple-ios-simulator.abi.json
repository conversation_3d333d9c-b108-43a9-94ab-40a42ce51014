{"ABIRoot": {"kind": "Root", "name": "TopLevel", "printedName": "TopLevel", "children": [{"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "CallFeatureFactory", "printedName": "CallFeatureFactory", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(cls:)", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "τ_1_0.Type", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_1_0"}]}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC3clsACyxGqd__m_tcSo07ACSCallE0CRbd__lufc", "mangledName": "$s25AzureCommunicationCalling18CallFeatureFactoryC3clsACyxGqd__m_tcSo07ACSCallE0CRbd__lufc", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0, τ_1_0 where τ_0_0 : AzureCommunicationCalling.CallFeature, τ_1_0 : AzureCommunicationCalling.CallFeature>", "sugared_genericSig": "<T, TCallFeature where T : AzureCommunicationCalling.CallFeature, TCallFeature : AzureCommunicationCalling.CallFeature>", "init_kind": "Designated"}, {"kind": "Function", "name": "getCallFeatureImpl", "printedName": "getCallFeatureImpl()", "children": [{"kind": "TypeNominal", "name": "Metatype", "printedName": "AzureCommunicationCalling.CallFeature.Type", "children": [{"kind": "TypeNominal", "name": "CallFeature", "printedName": "AzureCommunicationCalling.CallFeature", "usr": "c:objc(cs)ACSCallFeature"}]}], "declKind": "Func", "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC03getdE4ImplSo07ACSCallE0CmyF", "mangledName": "$s25AzureCommunicationCalling18CallFeatureFactoryC03getdE4ImplSo07ACSCallE0CmyF", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.CallFeature>", "sugared_genericSig": "<T where T : AzureCommunicationCalling.CallFeature>", "isOpen": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC", "mangledName": "$s25AzureCommunicationCalling18CallFeatureFactoryC", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.CallFeature>", "sugared_genericSig": "<T where T : AzureCommunicationCalling.CallFeature>", "isOpen": true, "declAttributes": ["AccessControl"], "hasMissingDesignatedInitializers": true}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"]}, {"kind": "Import", "name": "Foundation", "printedName": "Foundation", "declKind": "Import", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"]}, {"kind": "TypeDecl", "name": "LocalVideoStreamFeatureFactory", "printedName": "LocalVideoStreamFeatureFactory", "children": [{"kind": "<PERSON><PERSON><PERSON><PERSON>", "name": "init", "printedName": "init(cls:)", "children": [{"kind": "TypeNominal", "name": "LocalVideoStreamFeatureFactory", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeatureFactory<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC"}, {"kind": "TypeNominal", "name": "Metatype", "printedName": "τ_1_0.Type", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_1_0"}]}], "declKind": "<PERSON><PERSON><PERSON><PERSON>", "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC3clsACyxGqd__m_tcSo08ACSLocalefG0CRbd__lufc", "mangledName": "$s25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC3clsACyxGqd__m_tcSo08ACSLocalefG0CRbd__lufc", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0, τ_1_0 where τ_0_0 : AzureCommunicationCalling.LocalVideoStreamFeature, τ_1_0 : AzureCommunicationCalling.LocalVideoStreamFeature>", "sugared_genericSig": "<T, TLocalVideoStreamFeature where T : AzureCommunicationCalling.LocalVideoStreamFeature, TLocalVideoStreamFeature : AzureCommunicationCalling.LocalVideoStreamFeature>", "init_kind": "Designated"}, {"kind": "Function", "name": "getLocalVideoStreamFeatureImpl", "printedName": "getLocalVideoStreamFeatureImpl()", "children": [{"kind": "TypeNominal", "name": "Metatype", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeature.Type", "children": [{"kind": "TypeNominal", "name": "LocalVideoStreamFeature", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeature", "usr": "c:objc(cs)ACSLocalVideoStreamFeature"}]}], "declKind": "Func", "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC03getdefG4ImplSo08ACSLocalefG0CmyF", "mangledName": "$s25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC03getdefG4ImplSo08ACSLocalefG0CmyF", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.LocalVideoStreamFeature>", "sugared_genericSig": "<T where T : AzureCommunicationCalling.LocalVideoStreamFeature>", "isOpen": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC", "mangledName": "$s25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.LocalVideoStreamFeature>", "sugared_genericSig": "<T where T : AzureCommunicationCalling.LocalVideoStreamFeature>", "isOpen": true, "declAttributes": ["AccessControl"], "hasMissingDesignatedInitializers": true}, {"kind": "TypeDecl", "name": "CommonCall", "printedName": "CommonCall", "children": [{"kind": "Function", "name": "feature", "printedName": "feature(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Func", "usr": "s:So13ACSCommonCallC25AzureCommunicationCallingE7featureyxAC0B14FeatureFactoryCyxGSo07ACSCallG0CRbzlF", "mangledName": "$sSo13ACSCommonCallC25AzureCommunicationCallingE7featureyxAC0B14FeatureFactoryCyxGSo07ACSCallG0CRbzlF", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.CallFeature>", "sugared_genericSig": "<TCallFeature where TCallFeature : AzureCommunicationCalling.CallFeature>", "isOpen": true, "declAttributes": ["AccessControl"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)ACSCommonCall", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSCommonCall", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "MediaDiagnosticValues", "printedName": "MediaDiagnosticValues", "children": [{"kind": "Var", "name": "isSpeakerNotFunctioning", "printedName": "isSpeakerNotFunctioning", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE23isSpeakerNotFunctioningSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE23isSpeakerNotFunctioningSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE23isSpeakerNotFunctioningSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE23isSpeakerNotFunctioningSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isSpeakerBusy", "printedName": "isSpeakerBusy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE13isSpeakerBusySbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE13isSpeakerBusySbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE13isSpeakerBusySbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE13isSpeakerBusySbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isSpeakerMuted", "printedName": "isSpeakerMuted", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isSpeakerMutedSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isSpeakerMutedSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isSpeakerMutedSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isSpeakerMutedSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isSpeakerVolumeZero", "printedName": "isSpeakerVolumeZero", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isSpeakerVolumeZeroSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isSpeakerVolumeZeroSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isSpeakerVolumeZeroSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isSpeakerVolumeZeroSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isNoSpeakerDevicesAvailable", "printedName": "isNoSpeakerDevicesAvailable", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE27isNoSpeakerDevicesAvailableSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE27isNoSpeakerDevicesAvailableSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE27isNoSpeakerDevicesAvailableSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE27isNoSpeakerDevicesAvailableSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isSpeakingWhileMicrophoneIsMuted", "printedName": "isSpeakingWhileMicrophoneIsMuted", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE32isSpeakingWhileMicrophoneIsMutedSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE32isSpeakingWhileMicrophoneIsMutedSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE32isSpeakingWhileMicrophoneIsMutedSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE32isSpeakingWhileMicrophoneIsMutedSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isNoMicrophoneDevicesAvailable", "printedName": "isNoMicrophoneDevicesAvailable", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE30isNoMicrophoneDevicesAvailableSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE30isNoMicrophoneDevicesAvailableSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE30isNoMicrophoneDevicesAvailableSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE30isNoMicrophoneDevicesAvailableSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isMicrophoneBusy", "printedName": "isMicrophoneBusy", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE16isMicrophoneBusySbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE16isMicrophoneBusySbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE16isMicrophoneBusySbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE16isMicrophoneBusySbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isCameraFreeze", "printedName": "isCameraFreeze", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isCameraFreezeSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isCameraFreezeSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isCameraFreezeSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE14isCameraFreezeSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isCameraStartFailed", "printedName": "isCameraStartFailed", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isCameraStartFailedSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isCameraStartFailedSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isCameraStartFailedSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE19isCameraStartFailedSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isCameraStartTimedOut", "printedName": "isCameraStartTimedOut", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE21isCameraStartTimedOutSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE21isCameraStartTimedOutSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE21isCameraStartTimedOutSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE21isCameraStartTimedOutSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isMicrophoneNotFunctioning", "printedName": "isMicrophoneNotFunctioning", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE26isMicrophoneNotFunctioningSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE26isMicrophoneNotFunctioningSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE26isMicrophoneNotFunctioningSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE26isMicrophoneNotFunctioningSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isMicrophoneMutedUnexpectedly", "printedName": "isMicrophoneMutedUnexpectedly", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE29isMicrophoneMutedUnexpectedlySbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE29isMicrophoneMutedUnexpectedlySbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE29isMicrophoneMutedUnexpectedlySbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE29isMicrophoneMutedUnexpectedlySbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isCameraPermissionDenied", "printedName": "isCameraPermissionDenied", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE24isCameraPermissionDeniedSbSgvp", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE24isCameraPermissionDeniedSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE24isCameraPermissionDeniedSbSgvg", "mangledName": "$sSo24ACSMediaDiagnosticValuesC25AzureCommunicationCallingE24isCameraPermissionDeniedSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSMediaDiagnosticValues", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSMediaDiagnosticValues", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "NetworkDiagnosticValues", "printedName": "NetworkDiagnosticValues", "children": [{"kind": "Var", "name": "isNetworkUnavailable", "printedName": "isNetworkUnavailable", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE20isNetworkUnavailableSbSgvp", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE20isNetworkUnavailableSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE20isNetworkUnavailableSbSgvg", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE20isNetworkUnavailableSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "isNetworkRelaysUnreachable", "printedName": "isNetworkRelaysUnreachable", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26isNetworkRelaysUnreachableSbSgvp", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26isNetworkRelaysUnreachableSbSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Bool?", "children": [{"kind": "TypeNominal", "name": "Bool", "printedName": "Swift.Bool", "usr": "s:Sb"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26isNetworkRelaysUnreachableSbSgvg", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26isNetworkRelaysUnreachableSbSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "networkReconnectionQuality", "printedName": "networkReconnectionQuality", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Var", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26networkReconnectionQualitySo013ACSDiagnosticI0Vvp", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26networkReconnectionQualitySo013ACSDiagnosticI0Vvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Accessor", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26networkReconnectionQualitySo013ACSDiagnosticI0Vvg", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE26networkReconnectionQualitySo013ACSDiagnosticI0Vvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "networkSendQuality", "printedName": "networkSendQuality", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Var", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE18networkSendQualitySo013ACSDiagnosticI0Vvp", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE18networkSendQualitySo013ACSDiagnosticI0Vvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Accessor", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE18networkSendQualitySo013ACSDiagnosticI0Vvg", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE18networkSendQualitySo013ACSDiagnosticI0Vvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "networkReceiveQuality", "printedName": "networkReceiveQuality", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Var", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE21networkReceiveQualitySo013ACSDiagnosticI0Vvp", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE21networkReceiveQualitySo013ACSDiagnosticI0Vvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "DiagnosticQuality", "printedName": "AzureCommunicationCalling.DiagnosticQuality", "usr": "c:@E@ACSDiagnosticQuality"}], "declKind": "Accessor", "usr": "s:So26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE21networkReceiveQualitySo013ACSDiagnosticI0Vvg", "mangledName": "$sSo26ACSNetworkDiagnosticValuesC25AzureCommunicationCallingE21networkReceiveQualitySo013ACSDiagnosticI0Vvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSNetworkDiagnosticValues", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSNetworkDiagnosticValues", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "Features", "printedName": "Features", "children": [{"kind": "Var", "name": "recording", "printedName": "recording", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RecordingCallFeature>", "children": [{"kind": "TypeNominal", "name": "RecordingCallFeature", "printedName": "AzureCommunicationCalling.RecordingCallFeature", "usr": "c:objc(cs)ACSRecordingCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RecordingCallFeature>", "children": [{"kind": "TypeNominal", "name": "RecordingCallFeature", "printedName": "AzureCommunicationCalling.RecordingCallFeature", "usr": "c:objc(cs)ACSRecordingCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RecordingCallFeature>", "children": [{"kind": "TypeNominal", "name": "RecordingCallFeature", "printedName": "AzureCommunicationCalling.RecordingCallFeature", "usr": "c:objc(cs)ACSRecordingCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9recordingAC18CallFeatureFactoryCySo012ACSRecordingfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "transcription", "printedName": "transcription", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.TranscriptionCallFeature>", "children": [{"kind": "TypeNominal", "name": "TranscriptionCallFeature", "printedName": "AzureCommunicationCalling.TranscriptionCallFeature", "usr": "c:objc(cs)ACSTranscriptionCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.TranscriptionCallFeature>", "children": [{"kind": "TypeNominal", "name": "TranscriptionCallFeature", "printedName": "AzureCommunicationCalling.TranscriptionCallFeature", "usr": "c:objc(cs)ACSTranscriptionCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.TranscriptionCallFeature>", "children": [{"kind": "TypeNominal", "name": "TranscriptionCallFeature", "printedName": "AzureCommunicationCalling.TranscriptionCallFeature", "usr": "c:objc(cs)ACSTranscriptionCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE13transcriptionAC18CallFeatureFactoryCySo016ACSTranscriptionfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dominantSpeakers", "printedName": "dominantSpeakers", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DominantSpeakersCallFeature>", "children": [{"kind": "TypeNominal", "name": "DominantSpeakersCallFeature", "printedName": "AzureCommunicationCalling.DominantSpeakersCallFeature", "usr": "c:objc(cs)ACSDominantSpeakersCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DominantSpeakersCallFeature>", "children": [{"kind": "TypeNominal", "name": "DominantSpeakersCallFeature", "printedName": "AzureCommunicationCalling.DominantSpeakersCallFeature", "usr": "c:objc(cs)ACSDominantSpeakersCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DominantSpeakersCallFeature>", "children": [{"kind": "TypeNominal", "name": "DominantSpeakersCallFeature", "printedName": "AzureCommunicationCalling.DominantSpeakersCallFeature", "usr": "c:objc(cs)ACSDominantSpeakersCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE16dominantSpeakersAC18CallFeatureFactoryCySo011ACSDominantfgH0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "raisedHands", "printedName": "raisedHands", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RaiseHandCallFeature>", "children": [{"kind": "TypeNominal", "name": "RaiseHandCallFeature", "printedName": "AzureCommunicationCalling.RaiseHandCallFeature", "usr": "c:objc(cs)ACSRaiseHandCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RaiseHandCallFeature>", "children": [{"kind": "TypeNominal", "name": "RaiseHandCallFeature", "printedName": "AzureCommunicationCalling.RaiseHandCallFeature", "usr": "c:objc(cs)ACSRaiseHandCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RaiseHandCallFeature>", "children": [{"kind": "TypeNominal", "name": "RaiseHandCallFeature", "printedName": "AzureCommunicationCalling.RaiseHandCallFeature", "usr": "c:objc(cs)ACSRaiseHandCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11raisedHandsAC18CallFeatureFactoryCySo012ACSRaiseHandgH0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "captions", "printedName": "captions", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CaptionsCallFeature>", "children": [{"kind": "TypeNominal", "name": "CaptionsCallFeature", "printedName": "AzureCommunicationCalling.CaptionsCallFeature", "usr": "c:objc(cs)ACSCaptionsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CaptionsCallFeature>", "children": [{"kind": "TypeNominal", "name": "CaptionsCallFeature", "printedName": "AzureCommunicationCalling.CaptionsCallFeature", "usr": "c:objc(cs)ACSCaptionsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CaptionsCallFeature>", "children": [{"kind": "TypeNominal", "name": "CaptionsCallFeature", "printedName": "AzureCommunicationCalling.CaptionsCallFeature", "usr": "c:objc(cs)ACSCaptionsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE8captionsAC18CallFeatureFactoryCySo011ACSCaptionsfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "localVideoEffects", "printedName": "localVideoEffects", "children": [{"kind": "TypeNominal", "name": "LocalVideoStreamFeatureFactory", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeatureFactory<AzureCommunicationCalling.LocalVideoEffectsFeature>", "children": [{"kind": "TypeNominal", "name": "LocalVideoEffectsFeature", "printedName": "AzureCommunicationCalling.LocalVideoEffectsFeature", "usr": "c:objc(cs)ACSLocalVideoEffectsFeature"}], "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "LocalVideoStreamFeatureFactory", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeatureFactory<AzureCommunicationCalling.LocalVideoEffectsFeature>", "children": [{"kind": "TypeNominal", "name": "LocalVideoEffectsFeature", "printedName": "AzureCommunicationCalling.LocalVideoEffectsFeature", "usr": "c:objc(cs)ACSLocalVideoEffectsFeature"}], "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "LocalVideoStreamFeatureFactory", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeatureFactory<AzureCommunicationCalling.LocalVideoEffectsFeature>", "children": [{"kind": "TypeNominal", "name": "LocalVideoEffectsFeature", "printedName": "AzureCommunicationCalling.LocalVideoEffectsFeature", "usr": "c:objc(cs)ACSLocalVideoEffectsFeature"}], "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE17localVideoEffectsAC05LocalF20StreamFeatureFactoryCySo08ACSLocalfgJ0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "localUserDiagnostics", "printedName": "localUserDiagnostics", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.LocalUserDiagnosticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "LocalUserDiagnosticsCallFeature", "printedName": "AzureCommunicationCalling.LocalUserDiagnosticsCallFeature", "usr": "c:objc(cs)ACSLocalUserDiagnosticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.LocalUserDiagnosticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "LocalUserDiagnosticsCallFeature", "printedName": "AzureCommunicationCalling.LocalUserDiagnosticsCallFeature", "usr": "c:objc(cs)ACSLocalUserDiagnosticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.LocalUserDiagnosticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "LocalUserDiagnosticsCallFeature", "printedName": "AzureCommunicationCalling.LocalUserDiagnosticsCallFeature", "usr": "c:objc(cs)ACSLocalUserDiagnosticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE20localUserDiagnosticsAC18CallFeatureFactoryCySo08ACSLocalfghI0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "spotlight", "printedName": "spotlight", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SpotlightCallFeature>", "children": [{"kind": "TypeNominal", "name": "SpotlightCallFeature", "printedName": "AzureCommunicationCalling.SpotlightCallFeature", "usr": "c:objc(cs)ACSSpotlightCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SpotlightCallFeature>", "children": [{"kind": "TypeNominal", "name": "SpotlightCallFeature", "printedName": "AzureCommunicationCalling.SpotlightCallFeature", "usr": "c:objc(cs)ACSSpotlightCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SpotlightCallFeature>", "children": [{"kind": "TypeNominal", "name": "SpotlightCallFeature", "printedName": "AzureCommunicationCalling.SpotlightCallFeature", "usr": "c:objc(cs)ACSSpotlightCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE9spotlightAC18CallFeatureFactoryCySo012ACSSpotlightfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "mediaStatistics", "printedName": "mediaStatistics", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.MediaStatisticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "MediaStatisticsCallFeature", "printedName": "AzureCommunicationCalling.MediaStatisticsCallFeature", "usr": "c:objc(cs)ACSMediaStatisticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.MediaStatisticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "MediaStatisticsCallFeature", "printedName": "AzureCommunicationCalling.MediaStatisticsCallFeature", "usr": "c:objc(cs)ACSMediaStatisticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.MediaStatisticsCallFeature>", "children": [{"kind": "TypeNominal", "name": "MediaStatisticsCallFeature", "printedName": "AzureCommunicationCalling.MediaStatisticsCallFeature", "usr": "c:objc(cs)ACSMediaStatisticsCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE15mediaStatisticsAC18CallFeatureFactoryCySo08ACSMediafgH0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "dataChannel", "printedName": "dataChannel", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DataChannelCallFeature>", "children": [{"kind": "TypeNominal", "name": "DataChannelCallFeature", "printedName": "AzureCommunicationCalling.DataChannelCallFeature", "usr": "c:objc(cs)ACSDataChannelCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DataChannelCallFeature>", "children": [{"kind": "TypeNominal", "name": "DataChannelCallFeature", "printedName": "AzureCommunicationCalling.DataChannelCallFeature", "usr": "c:objc(cs)ACSDataChannelCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.DataChannelCallFeature>", "children": [{"kind": "TypeNominal", "name": "DataChannelCallFeature", "printedName": "AzureCommunicationCalling.DataChannelCallFeature", "usr": "c:objc(cs)ACSDataChannelCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE11dataChannelAC18CallFeatureFactoryCySo07ACSDatafgH0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "capabilities", "printedName": "capabilities", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CapabilitiesCallFeature>", "children": [{"kind": "TypeNominal", "name": "CapabilitiesCallFeature", "printedName": "AzureCommunicationCalling.CapabilitiesCallFeature", "usr": "c:objc(cs)ACSCapabilitiesCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CapabilitiesCallFeature>", "children": [{"kind": "TypeNominal", "name": "CapabilitiesCallFeature", "printedName": "AzureCommunicationCalling.CapabilitiesCallFeature", "usr": "c:objc(cs)ACSCapabilitiesCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.CapabilitiesCallFeature>", "children": [{"kind": "TypeNominal", "name": "CapabilitiesCallFeature", "printedName": "AzureCommunicationCalling.CapabilitiesCallFeature", "usr": "c:objc(cs)ACSCapabilitiesCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12capabilitiesAC18CallFeatureFactoryCySo015ACSCapabilitiesfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "survey", "printedName": "survey", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SurveyCallFeature>", "children": [{"kind": "TypeNominal", "name": "SurveyCallFeature", "printedName": "AzureCommunicationCalling.SurveyCallFeature", "usr": "c:objc(cs)ACSSurveyCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SurveyCallFeature>", "children": [{"kind": "TypeNominal", "name": "SurveyCallFeature", "printedName": "AzureCommunicationCalling.SurveyCallFeature", "usr": "c:objc(cs)ACSSurveyCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.SurveyCallFeature>", "children": [{"kind": "TypeNominal", "name": "SurveyCallFeature", "printedName": "AzureCommunicationCalling.SurveyCallFeature", "usr": "c:objc(cs)ACSSurveyCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE6surveyAC18CallFeatureFactoryCySo09ACSSurveyfG0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}, {"kind": "Var", "name": "realTimeText", "printedName": "realTimeText", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RealTimeTextCallFeature>", "children": [{"kind": "TypeNominal", "name": "RealTimeTextCallFeature", "printedName": "AzureCommunicationCalling.RealTimeTextCallFeature", "usr": "c:objc(cs)ACSRealTimeTextCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Var", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvpZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["HasInitialValue", "Final", "HasStorage", "AccessControl"], "isFromExtension": true, "hasStorage": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RealTimeTextCallFeature>", "children": [{"kind": "TypeNominal", "name": "RealTimeTextCallFeature", "printedName": "AzureCommunicationCalling.RealTimeTextCallFeature", "usr": "c:objc(cs)ACSRealTimeTextCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvgZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "get"}, {"kind": "Accessor", "name": "Set", "printedName": "Set()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}, {"kind": "TypeNominal", "name": "CallFeatureFactory", "printedName": "AzureCommunicationCalling.CallFeatureFactory<AzureCommunicationCalling.RealTimeTextCallFeature>", "children": [{"kind": "TypeNominal", "name": "RealTimeTextCallFeature", "printedName": "AzureCommunicationCalling.RealTimeTextCallFeature", "usr": "c:objc(cs)ACSRealTimeTextCallFeature"}], "usr": "s:25AzureCommunicationCalling18CallFeatureFactoryC"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvsZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvsZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "set"}, {"kind": "Accessor", "name": "Modify", "printedName": "Modify()", "children": [{"kind": "TypeNominal", "name": "Void", "printedName": "()"}], "declKind": "Accessor", "usr": "s:So11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvMZ", "mangledName": "$sSo11ACSFeaturesC25AzureCommunicationCallingE12realTimeTextAC18CallFeatureFactoryCySo07ACSRealfghI0CGvMZ", "moduleName": "AzureCommunicationCalling", "static": true, "implicit": true, "declAttributes": ["Final"], "isFromExtension": true, "accessorKind": "_modify"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSFeatures", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSFeatures", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "LocalVideoStream", "printedName": "LocalVideoStream", "children": [{"kind": "Function", "name": "feature", "printedName": "feature(_:)", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}, {"kind": "TypeNominal", "name": "LocalVideoStreamFeatureFactory", "printedName": "AzureCommunicationCalling.LocalVideoStreamFeatureFactory<τ_0_0>", "children": [{"kind": "TypeNominal", "name": "GenericTypeParam", "printedName": "τ_0_0"}], "usr": "s:25AzureCommunicationCalling30LocalVideoStreamFeatureFactoryC"}], "declKind": "Func", "usr": "s:So19ACSLocalVideoStreamC25AzureCommunicationCallingE7featureyxAC05LocalbC14FeatureFactoryCyxGSo0abcI0CRbzlF", "mangledName": "$sSo19ACSLocalVideoStreamC25AzureCommunicationCallingE7featureyxAC05LocalbC14FeatureFactoryCyxGSo0abcI0CRbzlF", "moduleName": "AzureCommunicationCalling", "genericSig": "<τ_0_0 where τ_0_0 : AzureCommunicationCalling.LocalVideoStreamFeature>", "sugared_genericSig": "<TLocalVideoStreamFeature where TLocalVideoStreamFeature : AzureCommunicationCalling.LocalVideoStreamFeature>", "isOpen": true, "declAttributes": ["AccessControl"], "isFromExtension": true, "funcSelfKind": "NonMutating"}], "declKind": "Class", "usr": "c:objc(cs)ACSLocalVideoStream", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSLocalVideoStream", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)ACSOutgoingVideoStream", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["AzureCommunicationCalling.OutgoingVideoStream", "AzureCommunicationCalling.CallVideoStream", "ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "OutgoingAudioStatistics", "printedName": "OutgoingAudioStatistics", "children": [{"kind": "Var", "name": "bitrateInBps", "printedName": "bitrateInBps", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "jitterInMs", "printedName": "jitterInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSOutgoingAudioStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSOutgoingAudioStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "IncomingAudioStatistics", "printedName": "IncomingAudioStatistics", "children": [{"kind": "Var", "name": "jitterInMs", "printedName": "jitterInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetsLostPerSecond", "printedName": "packetsLostPerSecond", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo26ACSIncomingAudioStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSIncomingAudioStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSIncomingAudioStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "IncomingVideoStatistics", "printedName": "IncomingVideoStatistics", "children": [{"kind": "Var", "name": "bitrateInBps", "printedName": "bitrateInBps", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "jitterInMs", "printedName": "jitterInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetsLostPerSecond", "printedName": "packetsLostPerSecond", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameRate", "printedName": "frameRate", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameWidth", "printedName": "frameWidth", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameHeight", "printedName": "frameHeight", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "totalFreezeDurationInMs", "printedName": "totalFreezeDurationInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvp", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvg", "mangledName": "$sSo26ACSIncomingVideoStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSIncomingVideoStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSIncomingVideoStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "IncomingScreenShareStatistics", "printedName": "IncomingScreenShareStatistics", "children": [{"kind": "Var", "name": "bitrateInBps", "printedName": "bitrateInBps", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "jitterInMs", "printedName": "jitterInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetsLostPerSecond", "printedName": "packetsLostPerSecond", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE20packetsLostPerSeconds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameRate", "printedName": "frameRate", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameWidth", "printedName": "frameWidth", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameHeight", "printedName": "frameHeight", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "totalFreezeDurationInMs", "printedName": "totalFreezeDurationInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvp", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvg", "mangledName": "$sSo32ACSIncomingScreenShareStatisticsC25AzureCommunicationCallingE23totalFreezeDurationInMss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSIncomingScreenShareStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSIncomingScreenShareStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "OutgoingVideoStatistics", "printedName": "OutgoingVideoStatistics", "children": [{"kind": "Var", "name": "bitrateInBps", "printedName": "bitrateInBps", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameRate", "printedName": "frameRate", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameWidth", "printedName": "frameWidth", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameHeight", "printedName": "frameHeight", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo26ACSOutgoingVideoStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSOutgoingVideoStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSOutgoingVideoStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "OutgoingScreenShareStatistics", "printedName": "OutgoingScreenShareStatistics", "children": [{"kind": "Var", "name": "bitrateInBps", "printedName": "bitrateInBps", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE12bitrateInBpss5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameRate", "printedName": "frameRate", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE9frameRateSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameWidth", "printedName": "frameWidth", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE10frameWidths5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "frameHeight", "printedName": "frameHeight", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE11frameHeights5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "streamId", "printedName": "streamId", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingScreenShareStatisticsC25AzureCommunicationCallingE8streamIds5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSOutgoingScreenShareStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSOutgoingScreenShareStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "OutgoingDataChannelStatistics", "printedName": "OutgoingDataChannelStatistics", "children": [{"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSOutgoingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo32ACSOutgoingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSOutgoingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo32ACSOutgoingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSOutgoingDataChannelStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSOutgoingDataChannelStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "IncomingDataChannelStatistics", "printedName": "IncomingDataChannelStatistics", "children": [{"kind": "Var", "name": "jitterInMs", "printedName": "jitterInMs", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "mangledName": "$sSo32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Float?", "children": [{"kind": "TypeNominal", "name": "Float", "printedName": "Swift.Float", "usr": "s:Sf"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "mangledName": "$sSo32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE10jitterInMsSfSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "packetCount", "printedName": "packetCount", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Var", "usr": "s:So32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "mangledName": "$sSo32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvp", "moduleName": "AzureCommunicationCalling", "isOpen": true, "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Optional", "printedName": "Swift.Int32?", "children": [{"kind": "TypeNominal", "name": "Int32", "printedName": "Swift.Int32", "usr": "s:s5Int32V"}], "usr": "s:Sq"}], "declKind": "Accessor", "usr": "s:So32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "mangledName": "$sSo32ACSIncomingDataChannelStatisticsC25AzureCommunicationCallingE11packetCounts5Int32VSgvg", "moduleName": "AzureCommunicationCalling", "isOpen": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Class", "usr": "c:objc(cs)ACSIncomingDataChannelStatistics", "moduleName": "AzureCommunicationCalling", "isOpen": true, "objc_name": "ACSIncomingDataChannelStatistics", "declAttributes": ["ObjC", "Dynamic"], "superclassUsr": "c:objc(cs)NSObject", "isExternal": true, "inheritsConvenienceInitializers": true, "superclassNames": ["ObjectiveC.NSObject"], "conformances": [{"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CVarArg", "printedName": "CVarArg", "usr": "s:s7CVarArgP", "mangledName": "$ss7CVarArgP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObservingPublishing", "printedName": "_KeyValueCodingAndObservingPublishing", "usr": "s:10Foundation37_KeyValueCodingAndObservingPublishingP", "mangledName": "$s10Foundation37_KeyValueCodingAndObservingPublishingP"}, {"kind": "Conformance", "name": "_KeyValueCodingAndObserving", "printedName": "_KeyValueCodingAndObserving", "usr": "s:10Foundation27_KeyValueCodingAndObservingP", "mangledName": "$s10Foundation27_KeyValueCodingAndObservingP"}, {"kind": "Conformance", "name": "CustomStringConvertible", "printedName": "CustomStringConvertible", "usr": "s:s23CustomStringConvertibleP", "mangledName": "$ss23CustomStringConvertibleP"}, {"kind": "Conformance", "name": "CustomDebugStringConvertible", "printedName": "CustomDebugStringConvertible", "usr": "s:s28CustomDebugStringConvertibleP", "mangledName": "$ss28CustomDebugStringConvertibleP"}]}, {"kind": "TypeDecl", "name": "AudioStreamChannelMode", "printedName": "AudioStreamChannelMode", "children": [{"kind": "Var", "name": "channelCount", "printedName": "channelCount", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}], "declKind": "Var", "usr": "s:So25ACSAudioStreamChannelModeV25AzureCommunicationCallingE12channelCounts6UInt32Vvp", "mangledName": "$sSo25ACSAudioStreamChannelModeV25AzureCommunicationCallingE12channelCounts6UInt32Vvp", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "UInt32", "printedName": "Swift.UInt32", "usr": "s:s6UInt32V"}], "declKind": "Accessor", "usr": "s:So25ACSAudioStreamChannelModeV25AzureCommunicationCallingE12channelCounts6UInt32Vvg", "mangledName": "$sSo25ACSAudioStreamChannelModeV25AzureCommunicationCallingE12channelCounts6UInt32Vvg", "moduleName": "AzureCommunicationCalling", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "allCases", "printedName": "allCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamChannelMode]", "children": [{"kind": "TypeNominal", "name": "AudioStreamChannelMode", "printedName": "AzureCommunicationCalling.AudioStreamChannelMode", "usr": "c:@E@ACSAudioStreamChannelMode"}], "usr": "s:Sa"}], "declKind": "Var", "usr": "s:So25ACSAudioStreamChannelModeV25AzureCommunicationCallingE8allCasesSayABGvpZ", "mangledName": "$sSo25ACSAudioStreamChannelModeV25AzureCommunicationCallingE8allCasesSayABGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["AccessControl"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamChannelMode]", "children": [{"kind": "TypeNominal", "name": "AudioStreamChannelMode", "printedName": "AzureCommunicationCalling.AudioStreamChannelMode", "usr": "c:@E@ACSAudioStreamChannelMode"}], "usr": "s:Sa"}], "declKind": "Accessor", "usr": "s:So25ACSAudioStreamChannelModeV25AzureCommunicationCallingE8allCasesSayABGvgZ", "mangledName": "$sSo25ACSAudioStreamChannelModeV25AzureCommunicationCallingE8allCasesSayABGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "c:@E@ACSAudioStreamChannelMode", "moduleName": "AzureCommunicationCalling", "objc_name": "ACSAudioStreamChannelMode", "declAttributes": ["SynthesizedProtocol", "ObjC", "SynthesizedProtocol", "Sendable", "Dynamic"], "enumRawTypeName": "Int", "isExternal": true, "conformances": [{"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CaseIterable", "printedName": "CaseIterable", "children": [{"kind": "TypeWitness", "name": "AllCases", "printedName": "AllCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamChannelMode]", "children": [{"kind": "TypeNominal", "name": "AudioStreamChannelMode", "printedName": "AzureCommunicationCalling.AudioStreamChannelMode", "usr": "c:@E@ACSAudioStreamChannelMode"}], "usr": "s:Sa"}]}], "usr": "s:s12CaseIterableP", "mangledName": "$ss12CaseIterableP"}]}, {"kind": "TypeDecl", "name": "AudioStreamBufferDuration", "printedName": "AudioStreamBufferDuration", "children": [{"kind": "Var", "name": "value", "printedName": "value", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE5valueSivp", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE5valueSivp", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE5valueSivg", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE5valueSivg", "moduleName": "AzureCommunicationCalling", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "timeInterval", "printedName": "timeInterval", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "declKind": "Var", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE12timeIntervalSdvp", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE12timeIntervalSdvp", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Double", "printedName": "Swift.Double", "usr": "s:Sd"}], "declKind": "Accessor", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE12timeIntervalSdvg", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE12timeIntervalSdvg", "moduleName": "AzureCommunicationCalling", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "allCases", "printedName": "allCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamBufferDuration]", "children": [{"kind": "TypeNominal", "name": "AudioStreamBufferDuration", "printedName": "AzureCommunicationCalling.AudioStreamBufferDuration", "usr": "c:@E@ACSAudioStreamBufferDuration"}], "usr": "s:Sa"}], "declKind": "Var", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE8allCasesSayABGvpZ", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE8allCasesSayABGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["AccessControl"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamBufferDuration]", "children": [{"kind": "TypeNominal", "name": "AudioStreamBufferDuration", "printedName": "AzureCommunicationCalling.AudioStreamBufferDuration", "usr": "c:@E@ACSAudioStreamBufferDuration"}], "usr": "s:Sa"}], "declKind": "Accessor", "usr": "s:So28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE8allCasesSayABGvgZ", "mangledName": "$sSo28ACSAudioStreamBufferDurationV25AzureCommunicationCallingE8allCasesSayABGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "c:@E@ACSAudioStreamBufferDuration", "moduleName": "AzureCommunicationCalling", "objc_name": "ACSAudioStreamBufferDuration", "declAttributes": ["SynthesizedProtocol", "ObjC", "SynthesizedProtocol", "Sendable", "Dynamic"], "enumRawTypeName": "Int", "isExternal": true, "conformances": [{"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CaseIterable", "printedName": "CaseIterable", "children": [{"kind": "TypeWitness", "name": "AllCases", "printedName": "AllCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamBufferDuration]", "children": [{"kind": "TypeNominal", "name": "AudioStreamBufferDuration", "printedName": "AzureCommunicationCalling.AudioStreamBufferDuration", "usr": "c:@E@ACSAudioStreamBufferDuration"}], "usr": "s:Sa"}]}], "usr": "s:s12CaseIterableP", "mangledName": "$ss12CaseIterableP"}]}, {"kind": "TypeDecl", "name": "AudioStreamSampleRate", "printedName": "AudioStreamSampleRate", "children": [{"kind": "Var", "name": "valueInHz", "printedName": "valueInHz", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:So24ACSAudioStreamSampleRateV25AzureCommunicationCallingE9valueInHzSivp", "mangledName": "$sSo24ACSAudioStreamSampleRateV25AzureCommunicationCallingE9valueInHzSivp", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:So24ACSAudioStreamSampleRateV25AzureCommunicationCallingE9valueInHzSivg", "mangledName": "$sSo24ACSAudioStreamSampleRateV25AzureCommunicationCallingE9valueInHzSivg", "moduleName": "AzureCommunicationCalling", "isFromExtension": true, "accessorKind": "get"}]}, {"kind": "Var", "name": "allCases", "printedName": "allCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamSampleRate]", "children": [{"kind": "TypeNominal", "name": "AudioStreamSampleRate", "printedName": "AzureCommunicationCalling.AudioStreamSampleRate", "usr": "c:@E@ACSAudioStreamSampleRate"}], "usr": "s:Sa"}], "declKind": "Var", "usr": "s:So24ACSAudioStreamSampleRateV25AzureCommunicationCallingE8allCasesSayABGvpZ", "mangledName": "$sSo24ACSAudioStreamSampleRateV25AzureCommunicationCallingE8allCasesSayABGvpZ", "moduleName": "AzureCommunicationCalling", "static": true, "declAttributes": ["AccessControl"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamSampleRate]", "children": [{"kind": "TypeNominal", "name": "AudioStreamSampleRate", "printedName": "AzureCommunicationCalling.AudioStreamSampleRate", "usr": "c:@E@ACSAudioStreamSampleRate"}], "usr": "s:Sa"}], "declKind": "Accessor", "usr": "s:So24ACSAudioStreamSampleRateV25AzureCommunicationCallingE8allCasesSayABGvgZ", "mangledName": "$sSo24ACSAudioStreamSampleRateV25AzureCommunicationCallingE8allCasesSayABGvgZ", "moduleName": "AzureCommunicationCalling", "static": true, "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "c:@E@ACSAudioStreamSampleRate", "moduleName": "AzureCommunicationCalling", "objc_name": "ACSAudioStreamSampleRate", "declAttributes": ["SynthesizedProtocol", "ObjC", "SynthesizedProtocol", "Sendable", "Dynamic"], "enumRawTypeName": "Int", "isExternal": true, "conformances": [{"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}, {"kind": "Conformance", "name": "CaseIterable", "printedName": "CaseIterable", "children": [{"kind": "TypeWitness", "name": "AllCases", "printedName": "AllCases", "children": [{"kind": "TypeNominal", "name": "Array", "printedName": "[AzureCommunicationCalling.AudioStreamSampleRate]", "children": [{"kind": "TypeNominal", "name": "AudioStreamSampleRate", "printedName": "AzureCommunicationCalling.AudioStreamSampleRate", "usr": "c:@E@ACSAudioStreamSampleRate"}], "usr": "s:Sa"}]}], "usr": "s:s12CaseIterableP", "mangledName": "$ss12CaseIterableP"}]}, {"kind": "TypeDecl", "name": "AudioStreamFormat", "printedName": "AudioStreamFormat", "children": [{"kind": "Var", "name": "bytesPerSample", "printedName": "bytesPerSample", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Var", "usr": "s:So20ACSAudioStreamFormatV25AzureCommunicationCallingE14bytesPerSampleSivp", "mangledName": "$sSo20ACSAudioStreamFormatV25AzureCommunicationCallingE14bytesPerSampleSivp", "moduleName": "AzureCommunicationCalling", "declAttributes": ["RawDocComment"], "isFromExtension": true, "accessors": [{"kind": "Accessor", "name": "Get", "printedName": "Get()", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}], "declKind": "Accessor", "usr": "s:So20ACSAudioStreamFormatV25AzureCommunicationCallingE14bytesPerSampleSivg", "mangledName": "$sSo20ACSAudioStreamFormatV25AzureCommunicationCallingE14bytesPerSampleSivg", "moduleName": "AzureCommunicationCalling", "isFromExtension": true, "accessorKind": "get"}]}], "declKind": "Enum", "usr": "c:@E@ACSAudioStreamFormat", "moduleName": "AzureCommunicationCalling", "objc_name": "ACSAudioStreamFormat", "declAttributes": ["SynthesizedProtocol", "ObjC", "SynthesizedProtocol", "Sendable", "Dynamic"], "enumRawTypeName": "Int", "isExternal": true, "conformances": [{"kind": "Conformance", "name": "RawRepresentable", "printedName": "RawRepresentable", "children": [{"kind": "TypeWitness", "name": "RawValue", "printedName": "RawValue", "children": [{"kind": "TypeNominal", "name": "Int", "printedName": "Swift.Int", "usr": "s:<PERSON>"}]}], "usr": "s:SY", "mangledName": "$sSY"}, {"kind": "Conformance", "name": "Sendable", "printedName": "Sendable", "usr": "s:s8SendableP", "mangledName": "$ss8SendableP"}, {"kind": "Conformance", "name": "Equatable", "printedName": "Equatable", "usr": "s:SQ", "mangledName": "$sSQ"}, {"kind": "Conformance", "name": "<PERSON><PERSON><PERSON>", "printedName": "<PERSON><PERSON><PERSON>", "usr": "s:SH", "mangledName": "$sSH"}]}], "json_format_version": 8}, "ConstValues": [{"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 327, "length": 1, "value": "1"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 378, "length": 1, "value": "2"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 433, "length": 1, "value": "0"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 741, "length": 2, "value": "20"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 783, "length": 2, "value": "10"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 831, "length": 1, "value": "0"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1009, "length": 1, "value": "0"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1043, "length": 4, "value": "1000"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1067, "length": 1, "value": "0"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1373, "length": 5, "value": "16000"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1421, "length": 5, "value": "22050"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1469, "length": 5, "value": "24000"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1517, "length": 5, "value": "32000"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1565, "length": 5, "value": "44100"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1613, "length": 5, "value": "48000"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 1664, "length": 1, "value": "0"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 2059, "length": 1, "value": "2"}, {"filePath": "/Volumes/ServerHD2/agent/_work/1/s/SpoolCallingStack/native/sdk/iOS/ACSCallingSDK/RawAudioExtensions.swift", "kind": "IntegerLiteral", "offset": 2106, "length": 1, "value": "0"}]}