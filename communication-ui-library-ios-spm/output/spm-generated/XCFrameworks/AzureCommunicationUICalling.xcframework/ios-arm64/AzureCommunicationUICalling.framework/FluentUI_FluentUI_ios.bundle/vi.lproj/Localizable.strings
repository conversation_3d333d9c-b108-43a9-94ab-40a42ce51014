//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Đang tiến hành";
"Accessibility.ActivityIndicator.Stopped.label" = "Tiến độ tạm dừng";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Cảnh báo";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Bỏ";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Nhấn đúp để bỏ";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Đã xong";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Nhấn đúp để chuyển đổi lựa chọn";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Lịch";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Chọn ngày";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d sự kiện";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "không có sự kiện";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Tháng";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Ngày";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Năm";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Ngày";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Giờ";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Phút";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "SA/CH";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Tuần trong tháng";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Ngày trong tuần";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Đầu tiên";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Thứ hai";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Thứ ba";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Thứ tư";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Cuối cùng";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Bung rộng";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Nhấn đúp để bung rộng";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Thu gọn";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Nhấn đúp để thu gọn";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Đã xong";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Không thành công";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Đang tải";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d/%2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Hồ sơ tài khoản";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d trên %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Nhấn đúp để xem thêm hành động";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Xem thêm hành động";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Nhấn đúp để bật/tắt cài đặt";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Bật";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Tắt";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ mục";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, chưa đọc";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Xem thêm";

/* Generic label for cancel action */
"Common.Cancel" = "Hủy bỏ";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Được chia sẻ";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Chỉ mình tôi";

/* Just now date string */
"Date.Now" = "Vừa mới đây";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm trước";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh trước";

/* Yesterday string */
"Date.Yesterday" = "Hôm qua";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Hôm qua lúc %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ lúc %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Tìm kiếm trong thư mục";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Thời gian bắt đầu";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Thời gian kết thúc";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Ngày bắt đầu";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Ngày kết thúc";

/* Presence - Available status */
"Presence.Available" = "Trực tuyến";

/* Presence - Away status */
"Presence.Away" = "Vắng mặt";

/* Presence - Busy status */
"Presence.Busy" = "Bận";

/* Presence - Do not disturb status */
"Presence.DND" = "Đừng làm phiền";

/* Presence - Out of office status */
"Presence.OOF" = "Đi vắng";

/* Presence - Offline status */
"Presence.Offline" = "Ngoại tuyến";

/* Presence - Unknown status */
"Presence.Unknown" = "Không xác định";

/* Presence - Blocked status */
"Presence.Blocked" = "Bị chặn";
