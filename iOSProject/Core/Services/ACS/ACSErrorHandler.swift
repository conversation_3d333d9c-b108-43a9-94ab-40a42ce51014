//
//  ACSErrorHandler.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI

// MARK: - ACS Error Handler Protocol

/// Protocol for handling ACS errors and converting them to user-friendly messages
protocol ACSErrorHandlerProtocol {
    /// Handles an ACS error and returns appropriate user feedback
    func handleError(_ error: Error) -> Toast
    
    /// Creates a success toast for ACS operations
    func createSuccessToast(message: String) -> Toast
    
    /// Creates an info toast for ACS operations
    func createInfoToast(message: String) -> Toast
}

// MARK: - ACS Error Handler Implementation

/// Implementation of ACS error handler that integrates with the app's toast system
class ACSErrorHandler: ACSErrorHandlerProtocol {
    
    // MARK: - Error Handling
    
    func handleError(_ error: Error) -> Toast {
        if let acsError = error as? ACSError {
            return handleACSError(acsError)
        } else {
            return handleGenericError(error)
        }
    }
    
    func createSuccessToast(message: String) -> Toast {
        return Toast(
            type: .success,
            title: "Communication Success",
            message: message,
            duration: 3.0
        )
    }
    
    func createInfoToast(message: String) -> Toast {
        return Toast(
            type: .info,
            title: "Communication Info",
            message: message,
            duration: 4.0
        )
    }
    
    // MARK: - Private Methods
    
    private func handleACSError(_ error: ACSError) -> Toast {
        switch error {
        case .configurationInvalid:
            return Toast(
                type: .error,
                title: "Configuration Error",
                message: "Communication service is not properly configured. Please contact support.",
                duration: 6.0
            )
            
        case .tokenExpired:
            return Toast(
                type: .warning,
                title: "Session Expired",
                message: "Your communication session has expired. Please try again.",
                duration: 5.0
            )
            
        case .tokenGenerationFailed:
            return Toast(
                type: .error,
                title: "Authentication Failed",
                message: "Unable to authenticate for communication services. Please try again.",
                duration: 5.0
            )
            
        case .permissionDenied(let permission):
            return Toast(
                type: .warning,
                title: "Permission Required",
                message: "\(permission) permission is required for communication features. Please enable it in Settings.",
                duration: 7.0
            )
            
        case .networkError:
            return Toast(
                type: .error,
                title: "Network Error",
                message: "Please check your internet connection and try again.",
                duration: 5.0
            )
            
        case .callFailed(let message):
            return Toast(
                type: .error,
                title: "Call Failed",
                message: message.isEmpty ? "The communication session could not be established." : message,
                duration: 6.0
            )
            
        case .compositeInitializationFailed:
            return Toast(
                type: .error,
                title: "Service Unavailable",
                message: "Communication service is temporarily unavailable. Please try again later.",
                duration: 6.0
            )
            
        case .invalidCallConfiguration:
            return Toast(
                type: .error,
                title: "Invalid Configuration",
                message: "The communication session configuration is invalid. Please try again.",
                duration: 5.0
            )
        }
    }
    
    private func handleGenericError(_ error: Error) -> Toast {
        return Toast(
            type: .error,
            title: "Communication Error",
            message: error.localizedDescription,
            duration: 5.0
        )
    }
}

// MARK: - ACS Error Handler Extensions

extension ACSErrorHandler {
    
    /// Creates specific toasts for common ACS scenarios
    static func createCallStartedToast(callType: String) -> Toast {
        return Toast(
            type: .success,
            title: "Call Started",
            message: "\(callType) has been initiated successfully.",
            duration: 3.0
        )
    }
    
    static func createCallEndedToast() -> Toast {
        return Toast(
            type: .info,
            title: "Call Ended",
            message: "The communication session has ended.",
            duration: 3.0
        )
    }
    
    static func createPermissionGrantedToast() -> Toast {
        return Toast(
            type: .success,
            title: "Permissions Granted",
            message: "Camera and microphone access has been granted.",
            duration: 3.0
        )
    }
    
    static func createJoiningMeetingToast() -> Toast {
        return Toast(
            type: .info,
            title: "Joining Meeting",
            message: "Connecting to the meeting...",
            duration: 2.0
        )
    }

    /// Handle credential validation errors with user-friendly messages
    func handleCredentialValidationError(_ error: ACSCredentialValidationError) -> Toast {
        switch error {
        case .usingMockCredentials, .placeholderCredentials:
            return Toast(
                type: .error,
                title: "Configuration Error",
                message: "Communication service is not properly configured. Please contact support.",
                duration: 6.0
            )
        case .invalidConnectionString:
            return Toast(
                type: .error,
                title: "Configuration Error",
                message: "Invalid communication service configuration. Please contact support.",
                duration: 6.0
            )
        case .missingCredentials:
            return Toast(
                type: .error,
                title: "Configuration Error",
                message: "Communication service credentials are missing. Please contact support.",
                duration: 6.0
            )
        }
    }
}

// MARK: - ACS Error Handler Factory

/// Factory for creating ACS error handlers
struct ACSErrorHandlerFactory {
    
    static func create() -> ACSErrorHandlerProtocol {
        return ACSErrorHandler()
    }
}

// MARK: - View Model Error Handling Extension

extension ObservableObject {
    
    /// Helper method for view models to handle ACS errors consistently
    func handleACSError(_ error: Error, using navigationService: NavigationServiceProtocol) {
        let errorHandler = ACSErrorHandlerFactory.create()
        let toast = errorHandler.handleError(error)
        navigationService.showToast(toast)
    }
    
    /// Helper method for view models to show ACS success messages
    func showACSSuccess(_ message: String, using navigationService: NavigationServiceProtocol) {
        let errorHandler = ACSErrorHandlerFactory.create()
        let toast = errorHandler.createSuccessToast(message: message)
        navigationService.showToast(toast)
    }
    
    /// Helper method for view models to show ACS info messages
    func showACSInfo(_ message: String, using navigationService: NavigationServiceProtocol) {
        let errorHandler = ACSErrorHandlerFactory.create()
        let toast = errorHandler.createInfoToast(message: message)
        navigationService.showToast(toast)
    }
}
