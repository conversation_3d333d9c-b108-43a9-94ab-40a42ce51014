// swift-interface-format-version: 1.0
// swift-compiler-version: Apple Swift version 6.1.2 effective-5.10 (swiftlang-*******.2 clang-1700.0.13.5)
// swift-module-flags: -target arm64-apple-ios16.0 -enable-objc-interop -enable-library-evolution -swift-version 5 -enforce-exclusivity=checked -O -enable-experimental-feature DebugDescriptionMacro -enable-bare-slash-regex -module-name AzureCommunicationCommon
// swift-module-flags-ignorable: -no-verify-emitted-module-interface -interface-compiler-version 6.1.2
@_exported import AzureCommunicationCommon
import Foundation
import Swift
import _Concurrency
import _StringProcessing
import _SwiftConcurrencyShims
import os.log
import os
@objc @objcMembers public class CommunicationAccessToken : ObjectiveC.NSObject {
  @objc final public let token: Swift.String
  @objc final public let expiresOn: Foundation.Date
  @objc public init(token: Swift.String, expiresOn: Foundation.Date)
  @objc deinit
}
@objc @objcMembers public class CommunicationCloudEnvironment : ObjectiveC.NSObject {
  @objc public static let Public: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc public static let Dod: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc public static let Gcch: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc public init(environmentValue: Swift.String)
  @objc public func getEnvironmentValue() -> Swift.String
  public static func == (lhs: AzureCommunicationCommon.CommunicationCloudEnvironment, rhs: AzureCommunicationCommon.CommunicationCloudEnvironment) -> Swift.Bool
  @objc deinit
}
public typealias CommunicationTokenCompletionHandler = (AzureCommunicationCommon.CommunicationAccessToken?, (any Swift.Error)?) -> Swift.Void
public typealias TokenRefreshHandler = (Swift.String?, (any Swift.Error)?) -> Swift.Void
@objc @objcMembers public class CommunicationTokenCredential : ObjectiveC.NSObject, AzureCommunicationCommon.Cancellable {
  @objc public init(token: Swift.String) throws
  @objc public init(withOptions options: AzureCommunicationCommon.CommunicationTokenRefreshOptions) throws
  @objc public func token(completionHandler: @escaping AzureCommunicationCommon.CommunicationTokenCompletionHandler)
  @objc public func cancel()
  @objc deinit
}
public protocol Cancellable {
  func cancel()
}
public protocol CommunicationTokenCredentialProviding : AzureCommunicationCommon.Cancellable {
  func token(completionHandler: @escaping AzureCommunicationCommon.CommunicationTokenCompletionHandler)
}
public typealias TokenRefresher = (@escaping AzureCommunicationCommon.TokenRefreshHandler) -> Swift.Void
@objc @objcMembers public class CommunicationTokenRefreshOptions : ObjectiveC.NSObject {
  @objc public init(initialToken: Swift.String? = nil, refreshProactively: Swift.Bool = false, tokenRefresher: @escaping AzureCommunicationCommon.TokenRefresher)
  @objc deinit
}
@objc @objcMembers public class IdentifierKind : ObjectiveC.NSObject {
  @objc public static let communicationUser: AzureCommunicationCommon.IdentifierKind
  @objc public static let phoneNumber: AzureCommunicationCommon.IdentifierKind
  @objc public static let microsoftTeamsUser: AzureCommunicationCommon.IdentifierKind
  @objc public static let microsoftTeamsApp: AzureCommunicationCommon.IdentifierKind
  @objc public static let unknown: AzureCommunicationCommon.IdentifierKind
  @objc public init(rawValue: Swift.String)
  @objc deinit
}
@objc public protocol CommunicationIdentifier : ObjectiveC.NSObjectProtocol {
  @objc var rawId: Swift.String { get }
  @objc var kind: AzureCommunicationCommon.IdentifierKind { get }
}
public func createCommunicationIdentifier(fromRawId rawId: Swift.String) -> any AzureCommunicationCommon.CommunicationIdentifier
@objc @objcMembers public class CommunicationUserIdentifier : ObjectiveC.NSObject, AzureCommunicationCommon.CommunicationIdentifier {
  @objc public var rawId: Swift.String {
    @objc get
  }
  @objc public var kind: AzureCommunicationCommon.IdentifierKind {
    @objc get
  }
  @objc final public let identifier: Swift.String
  @objc(initWithIdentifier:) public init(_ identifier: Swift.String)
  @objc deinit
}
@objc @objcMembers public class UnknownIdentifier : ObjectiveC.NSObject, AzureCommunicationCommon.CommunicationIdentifier {
  @objc public var rawId: Swift.String {
    @objc get
  }
  @objc public var kind: AzureCommunicationCommon.IdentifierKind {
    @objc get
  }
  @objc final public let identifier: Swift.String
  @objc(initWithIdentifier:) public init(_ identifier: Swift.String)
  @objc deinit
}
@objc @objcMembers public class PhoneNumberIdentifier : ObjectiveC.NSObject, AzureCommunicationCommon.CommunicationIdentifier {
  @objc final public let phoneNumber: Swift.String
  @objc public var rawId: Swift.String {
    get
  }
  @objc public var kind: AzureCommunicationCommon.IdentifierKind {
    @objc get
  }
  @objc public init(phoneNumber: Swift.String, rawId: Swift.String? = nil)
  public static func == (lhs: AzureCommunicationCommon.PhoneNumberIdentifier, rhs: AzureCommunicationCommon.PhoneNumberIdentifier) -> Swift.Bool
  @objc override dynamic public func isEqual(_ object: Any?) -> Swift.Bool
  @objc deinit
}
@objc @objcMembers public class MicrosoftTeamsUserIdentifier : ObjectiveC.NSObject, AzureCommunicationCommon.CommunicationIdentifier {
  @objc final public let userId: Swift.String
  @objc final public let isAnonymous: Swift.Bool
  @objc public var rawId: Swift.String {
    get
  }
  @objc public var kind: AzureCommunicationCommon.IdentifierKind {
    @objc get
  }
  @objc @available(*, deprecated, renamed: "cloudEnvironment")
  final public let cloudEnviroment: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc final public let cloudEnvironment: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc public init(userId: Swift.String, isAnonymous: Swift.Bool = false, rawId: Swift.String? = nil, cloudEnvironment: AzureCommunicationCommon.CommunicationCloudEnvironment = .Public)
  public static func == (lhs: AzureCommunicationCommon.MicrosoftTeamsUserIdentifier, rhs: AzureCommunicationCommon.MicrosoftTeamsUserIdentifier) -> Swift.Bool
  @objc override dynamic public func isEqual(_ object: Any?) -> Swift.Bool
  @objc deinit
}
@objc @objcMembers public class MicrosoftTeamsAppIdentifier : ObjectiveC.NSObject, AzureCommunicationCommon.CommunicationIdentifier {
  @objc final public let appId: Swift.String
  @objc final public let cloudEnvironment: AzureCommunicationCommon.CommunicationCloudEnvironment
  @objc public var rawId: Swift.String
  @objc public var kind: AzureCommunicationCommon.IdentifierKind {
    @objc get
  }
  @objc public init(appId: Swift.String, cloudEnvironment: AzureCommunicationCommon.CommunicationCloudEnvironment = .Public)
  public static func == (lhs: AzureCommunicationCommon.MicrosoftTeamsAppIdentifier, rhs: AzureCommunicationCommon.MicrosoftTeamsAppIdentifier) -> Swift.Bool
  @objc override dynamic public func isEqual(_ object: Any?) -> Swift.Bool
  @objc deinit
}
