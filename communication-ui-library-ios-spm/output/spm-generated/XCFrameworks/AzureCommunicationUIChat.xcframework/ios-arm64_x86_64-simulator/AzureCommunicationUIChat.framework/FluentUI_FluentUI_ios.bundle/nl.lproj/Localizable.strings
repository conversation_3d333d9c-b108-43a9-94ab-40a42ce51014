//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Wordt uitgevoerd";
"Accessibility.ActivityIndicator.Stopped.label" = "Vooruitgang gestopt";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Waarschuwing";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Verwijderen";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dubbeltikken om te sluiten";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Gereed";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dubbeltikken om de selectie in of uit te schakelen";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Agenda";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Datum selecteren";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d gebeurtenissen";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "geen gebeurtenissen";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Maand";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dag";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Jaar";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Datum";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Uur";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuut";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Week van de maand";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dag van de week";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Eerste";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Tweede";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Derde";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Vierde";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Laatste";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Uitvouwen";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dubbeltikken om uit te vouwen";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Samenvouwen";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dubbeltikken om samen te vouwen";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Gereed";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Mislukt";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Laden";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d van %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Accountprofiel";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d van %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dubbeltikken om meer acties weer te geven";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Meer acties";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dubbeltikken om instelling in/uit te schakelen";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Aan";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Uit";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ items";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, ongelezen";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Meer";

/* Generic label for cancel action */
"Common.Cancel" = "Annuleren";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Gedeeld";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Alleen mezelf";

/* Just now date string */
"Date.Now" = "Zojuist";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm geleden";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh geleden";

/* Yesterday string */
"Date.Yesterday" = "Gisteren";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Gisteren om %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ om %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Zoeken in adreslijst";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Begintijd";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Eindtijd";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Begindatum";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Einddatum";

/* Presence - Available status */
"Presence.Available" = "Beschikbaar";

/* Presence - Away status */
"Presence.Away" = "Afwezig";

/* Presence - Busy status */
"Presence.Busy" = "Bezet";

/* Presence - Do not disturb status */
"Presence.DND" = "Niet storen";

/* Presence - Out of office status */
"Presence.OOF" = "Afwezig";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Onbekend";

/* Presence - Blocked status */
"Presence.Blocked" = "Geblokkeerd";
