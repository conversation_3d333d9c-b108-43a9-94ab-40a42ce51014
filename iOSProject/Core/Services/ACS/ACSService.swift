//
//  ACSService.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import AzureCommunicationUICalling
import AzureCommunicationCalling

// MARK: - ACS Service Protocol

/// Protocol defining Azure Communication Services functionality
protocol ACSServiceProtocol {
    /// Current call state
    var callState: ACSCallState { get }
    
    /// Starts a call with the specified configuration
    func startCall(with configuration: ACSCallConfiguration, from viewController: UIViewController?) async throws
    
    /// Joins a Teams meeting using the meeting URL
    func joinTeamsMeeting(url: String, displayName: String?, from viewController: UIViewController?) async throws
    
    /// Creates and starts a group call
    func createGroupCall(displayName: String?, from viewController: UIViewController?) async throws -> String
    
    /// Ends the current call
    func endCall() async throws
    
    /// Checks if camera and microphone permissions are granted
    func checkPermissions() async -> Bool
    
    /// Requests camera and microphone permissions
    func requestPermissions() async throws

    /// Validates that ACS credentials are properly configured
    func validateCredentials() -> ACSCredentialValidationResult
}

// MARK: - ACS Service Implementation

/// Implementation of Azure Communication Services functionality
class ACSService: ACSServiceProtocol, ObservableObject {
    
    // MARK: - Properties
    
    @Published private(set) var callState: ACSCallState = .idle
    
    private let configuration: ACSConfiguration
    private let tokenProvider: ACSTokenProviderProtocol
    private var callComposite: CallComposite?
    
    // MARK: - Initialization
    
    init(configuration: ACSConfiguration, tokenProvider: ACSTokenProviderProtocol) {
        self.configuration = configuration
        self.tokenProvider = tokenProvider
    }
    
    // MARK: - ACS Service Protocol Implementation
    
    func startCall(with configuration: ACSCallConfiguration, from viewController: UIViewController? = nil) async throws {
        try await performCallOperation {
            let composite = try await self.createCallComposite(displayName: configuration.displayName)
            
            let locator: JoinLocator
            
            switch configuration {
            case .groupCall(let groupId, _):
                guard let uuid = UUID(uuidString: groupId) else {
                    throw ACSError.invalidCallConfiguration
                }
                locator = .groupCall(groupId: uuid)
                
            case .teamsMeeting(let url, _):
                locator = .teamsMeeting(teamsLink: url)
                
            case .oneToOneCall(let userId, _):
                // For one-to-one calls, we'll use a group call with the user ID as group ID
                guard let uuid = UUID(uuidString: userId) else {
                    throw ACSError.invalidCallConfiguration
                }
                locator = .groupCall(groupId: uuid)
            }
            
            await MainActor.run {
                composite.launch(locator: locator)
            }
        }
    }
    
    func joinTeamsMeeting(url: String, displayName: String? = nil, from viewController: UIViewController? = nil) async throws {
        let config = ACSCallConfiguration.teamsMeeting(url: url, displayName: displayName)
        try await startCall(with: config, from: viewController)
    }
    
    func createGroupCall(displayName: String? = nil, from viewController: UIViewController? = nil) async throws -> String {
        let groupId = UUID().uuidString
        let config = ACSCallConfiguration.groupCall(groupId: groupId, displayName: displayName)
        try await startCall(with: config, from: viewController)
        return groupId
    }
    
    func endCall() async throws {
        await MainActor.run {
            callComposite?.dismiss()
            callComposite = nil
            callState = .disconnected
        }
    }
    
    func checkPermissions() async -> Bool {
        // Check camera and microphone permissions
        let cameraStatus = await checkCameraPermission()
        let microphoneStatus = await checkMicrophonePermission()
        
        return cameraStatus && microphoneStatus
    }

    func validateCredentials() -> ACSCredentialValidationResult {
        return configuration.validateCredentials()
    }

    func requestPermissions() async throws {
        // Request camera permission
        let cameraGranted = await requestCameraPermission()

        // Request microphone permission
        let microphoneGranted = await requestMicrophonePermission()

        if !cameraGranted {
            throw ACSError.permissionDenied("Camera")
        }

        if !microphoneGranted {
            throw ACSError.permissionDenied("Microphone")
        }
    }
    
    // MARK: - Private Methods
    
    private func createCallComposite(displayName: String) async throws -> CallComposite {
        do {
            let token = try await tokenProvider.refreshTokenIfNeeded()
            let communicationTokenCredential = try CommunicationTokenCredential(token: token)

            let callCompositeOptions = CallCompositeOptions(displayName: displayName)
            let composite = CallComposite(credential: communicationTokenCredential, withOptions: callCompositeOptions)

            // Set up event handlers
            setupEventHandlers(for: composite)

            self.callComposite = composite
            return composite

        } catch {
            await MainActor.run {
                callState = .failed(.compositeInitializationFailed)
            }
            throw ACSError.compositeInitializationFailed
        }
    }
    
    private func setupEventHandlers(for composite: CallComposite) {
        // Handle call state changes
        composite.events.onCallStateChanged = { [weak self] callStateEvent in
            DispatchQueue.main.async {
                self?.handleCallStateChange(callStateEvent)
            }
        }
        
        // Handle errors
        composite.events.onError = { [weak self] error in
            DispatchQueue.main.async {
                self?.handleCallError(error)
            }
        }
        
        // Handle dismissal
        composite.events.onDismissed = { [weak self] _ in
            DispatchQueue.main.async {
                self?.callState = .disconnected
                self?.callComposite = nil
            }
        }
    }
    
    private func handleCallStateChange(_ state: AzureCommunicationUICalling.CallState) {
        // Map ACS call states to our internal states
        // This is a simplified mapping - you might want to handle more states
        switch state {
        case .connecting:
            callState = .connecting
        case .connected:
            callState = .connected
        case .disconnecting:
            callState = .disconnecting
        case .disconnected:
            callState = .disconnected
        default:
            break
        }
    }
    
    private func handleCallError(_ error: CallCompositeError) {
        let acsError = ACSError.callFailed(error.code)
        callState = .failed(acsError)
    }
    
    private func performCallOperation(_ operation: @escaping () async throws -> Void) async throws {
        await MainActor.run {
            callState = .connecting
        }
        
        do {
            try await operation()
        } catch {
            await MainActor.run {
                if let acsError = error as? ACSError {
                    callState = .failed(acsError)
                } else {
                    callState = .failed(.callFailed(error.localizedDescription))
                }
            }
            throw error
        }
    }
    
    private func checkCameraPermission() async -> Bool {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        return status == .authorized
    }

    private func checkMicrophonePermission() async -> Bool {
        let status = AVAudioSession.sharedInstance().recordPermission
        return status == .granted
    }

    private func requestCameraPermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVCaptureDevice.requestAccess(for: .video) { granted in
                continuation.resume(returning: granted)
            }
        }
    }

    private func requestMicrophonePermission() async -> Bool {
        return await withCheckedContinuation { continuation in
            AVAudioSession.sharedInstance().requestRecordPermission { granted in
                continuation.resume(returning: granted)
            }
        }
    }
}
