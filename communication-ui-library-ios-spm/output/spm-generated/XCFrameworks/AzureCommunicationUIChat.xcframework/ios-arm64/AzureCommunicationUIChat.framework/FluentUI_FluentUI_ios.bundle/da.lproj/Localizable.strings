//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Igangværende";
"Accessibility.ActivityIndicator.Stopped.label" = "Status er standset";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Underretning";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Afvis";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Dobbelttryk for at afvise";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Udført";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dobbelttryk for at skifte valg ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalender";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Vælg en dato";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d begivenheder";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "ingen begivenheder";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Måned";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dag";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "År";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Dato";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Time";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minut";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Uge i måneden";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dag i ugen";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Første";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Anden";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Tredje";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Fjerde";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Sidste";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Udvid";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Dobbelttryk for at udvide";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Skjul";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Dobbelttryk for at skjule";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Udført";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Mislykkedes";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Indlæser";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d af %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Opret kontoprofil";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d af %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Dobbelttryk for at se flere handlinger";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Flere handlinger";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Dobbelttryk for at slå indstilling til/fra";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Til";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Fra";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elementer";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, ulæst";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Mere";

/* Generic label for cancel action */
"Common.Cancel" = "Annuller";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Delt";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Kun mig";

/* Just now date string */
"Date.Now" = "For et øjeblik siden";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm siden";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh siden";

/* Yesterday string */
"Date.Yesterday" = "I går";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "I går kl. %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ kl. %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Søg i mappen";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Starttidspunkt";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Sluttidspunkt";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Startdato";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Slutdato";

/* Presence - Available status */
"Presence.Available" = "Tilgængelig";

/* Presence - Away status */
"Presence.Away" = "Ikke til stede";

/* Presence - Busy status */
"Presence.Busy" = "Optaget";

/* Presence - Do not disturb status */
"Presence.DND" = "Vil ikke forstyrres";

/* Presence - Out of office status */
"Presence.OOF" = "Ikke til stede";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Ukendt";

/* Presence - Blocked status */
"Presence.Blocked" = "Blokeret";
