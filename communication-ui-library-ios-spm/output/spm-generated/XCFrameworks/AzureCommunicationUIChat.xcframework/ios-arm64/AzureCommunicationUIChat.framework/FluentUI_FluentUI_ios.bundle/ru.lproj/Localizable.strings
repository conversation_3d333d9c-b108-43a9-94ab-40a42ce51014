//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "Выполняется";
"Accessibility.ActivityIndicator.Stopped.label" = "Ход выполнения остановлен";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Оповещение";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Закрыть";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Дважды коснитесь, чтобы закрыть";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Готово";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Дважды коснитесь, чтобы переключить выделение ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Календарь";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Выберите дату";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "Событий: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "Нет событий";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Месяц";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "День";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Год";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Дата";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Час";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Минута";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Неделя месяца";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "День недели";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Первая";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Вторая";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Третья";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Четвертая";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Последняя";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Развернуть";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Коснитесь дважды, чтобы развернуть";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Свернуть";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Коснитесь дважды, чтобы свернуть";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Готово";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Сбой";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Загрузка";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d из %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Профиль учетной записи";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d из %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Дважды коснитесь, чтобы просмотреть другие действия";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Другие действия";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Дважды коснитесь, чтобы переключить параметр";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Вкл.";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Выкл.";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, элементов: %@";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, непрочитано";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Еще";

/* Generic label for cancel action */
"Common.Cancel" = "Отмена";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Общий доступ";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Доступно только мне";

/* Just now date string */
"Date.Now" = "Только что";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm назад";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh назад";

/* Yesterday string */
"Date.Yesterday" = "Вчера";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Вчера в %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ в %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Поиск в каталоге";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Время начала";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Время окончания";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Дата начала";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Дата окончания";

/* Presence - Available status */
"Presence.Available" = "Активен";

/* Presence - Away status */
"Presence.Away" = "Нет на месте";

/* Presence - Busy status */
"Presence.Busy" = "Занят";

/* Presence - Do not disturb status */
"Presence.DND" = "Не беспокоить";

/* Presence - Out of office status */
"Presence.OOF" = "Нет на рабочем месте";

/* Presence - Offline status */
"Presence.Offline" = "Вне сети";

/* Presence - Unknown status */
"Presence.Unknown" = "Неизвестно";

/* Presence - Blocked status */
"Presence.Blocked" = "Заблокирован";
