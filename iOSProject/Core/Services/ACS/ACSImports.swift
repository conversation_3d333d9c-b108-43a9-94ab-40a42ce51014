//
//  ACSImports.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation
import SwiftUI
import UIKit
import AVFoundation
import Combine
import os

// MARK: - Azure Communication Services Imports

/// This file centralizes all Azure Communication Services imports
/// The Azure SDK is now available after CocoaPods installation

import AzureCommunicationUICalling
import AzureCommunicationCalling
import AzureCom<PERSON><PERSON>ommon

// MARK: - Availability Checks

/// Utility struct to check Azure SDK availability
struct AzureSDKAvailability {
    
    /// Checks if the Azure Communication Services UI Calling library is available
    static var isUICallingAvailable: Bool {
        return true // Azure SDK is now installed
    }

    /// Checks if the Azure Communication Services Calling library is available
    static var isCallingAvailable: Bool {
        return true // Azure SDK is now installed
    }

    /// Checks if the Azure Communication Services Common library is available
    static var isCommonAvailable: Bool {
        return true // Azure SDK is now installed
    }

    /// Checks if all required Azure SDK components are available
    static var isFullyAvailable: Bool {
        return true // Azure SDK is now installed
    }
}

// MARK: - Logging Extensions

extension Logger {
    /// Logger for Azure Communication Services operations
    static let acs = Logger(subsystem: Bundle.main.bundleIdentifier ?? "iOSProject", category: "ACS")
}

// MARK: - ACS Constants

extension AppConstants {
    /// Azure Communication Services specific constants
    struct ACSInternal {
        static let sdkVersion = "2.0.0" // Update based on actual SDK version
        static let userAgent = "iOSProject-ACS/1.0"
        static let maxCallDuration: TimeInterval = 3600 // 1 hour
        static let connectionTimeout: TimeInterval = 30.0
        static let retryAttempts = 3
        static let retryDelay: TimeInterval = 2.0
    }
}
