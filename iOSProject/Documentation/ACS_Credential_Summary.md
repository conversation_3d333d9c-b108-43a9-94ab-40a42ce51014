# Azure Communication Services Credential Configuration Summary

## 🎯 **Quick Start Guide**

Your iOS project is now configured with a secure, environment-aware Azure Communication Services integration. Here's everything you need to know:

## 📍 **Key Files Modified/Created**

### 1. **Core Credential Management**
- `Core/Services/ACS/ACSCredentials.swift` - Secure credential loading and validation
- `Core/Services/ACS/ACSServiceFactory.swift` - Environment-aware service creation
- `Core/Services/ACS/ACSConfiguration.swift` - Updated to use new credential system

### 2. **Configuration Files**
- `Core/Resource/ACSConfig.plist` - Main configuration file (replace placeholders)
- `Scripts/setup_acs_environment.sh` - Setup script for credentials
- `Documentation/ACS_Setup_Guide.md` - Comprehensive setup guide

### 3. **Security**
- `.gitignore` - Updated to protect credential files
- Environment variable support for secure credential management

## 🔐 **Where to Place Your Real Credentials**

### **Option 1: Environment Variables (Recommended for Development)**

In Xcode:
1. Go to **Product → Scheme → Edit Scheme**
2. Select **Run → Arguments → Environment Variables**
3. Add these variables:

```
ACS_CONNECTION_STRING = endpoint=https://YOUR-RESOURCE.communication.azure.com/;accesskey=YOUR-KEY
ACS_ENDPOINT = https://YOUR-RESOURCE.communication.azure.com/
ACS_ACCESS_KEY = YOUR-ACCESS-KEY
ACS_RESOURCE_NAME = YOUR-RESOURCE-NAME
ACS_TOKEN_SERVICE_URL = https://your-backend.com/api/acs-token (optional)
```

### **Option 2: Configuration File (Recommended for Production)**

Edit `iOSProject/Core/Resource/ACSConfig.plist`:

```xml
<key>ConnectionString</key>
<string>endpoint=https://YOUR-RESOURCE.communication.azure.com/;accesskey=YOUR-KEY</string>

<key>Endpoint</key>
<string>https://YOUR-RESOURCE.communication.azure.com/</string>

<key>AccessKey</key>
<string>YOUR-ACCESS-KEY</string>

<key>ResourceName</key>
<string>YOUR-RESOURCE-NAME</string>
```

## 🔄 **Environment Switching**

The app automatically detects and switches between environments:

### **Development Mode**
- Uses mock service if credentials contain "your-" placeholders
- Uses real service if valid credentials are provided
- Console output: `🔧 Using Mock ACS Service for environment: Development`

### **Production Mode**
- Always uses real service
- Fails if mock credentials are detected
- Console output: `🚀 Using Real ACS Service for environment: Production`

## 🛠️ **Files That Need Real Credentials**

Replace placeholder values in these locations:

### 1. **ACSConfig.plist** (Primary)
```xml
<!-- Replace these placeholder values -->
<string>endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key</string>
<string>https://your-acs-resource.communication.azure.com/</string>
<string>your-access-key</string>
<string>your-acs-resource</string>
```

### 2. **Environment Variables** (Alternative)
```bash
# Replace these in your Xcode scheme
ACS_CONNECTION_STRING="endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key"
ACS_ENDPOINT="https://your-acs-resource.communication.azure.com/"
ACS_ACCESS_KEY="your-access-key"
ACS_RESOURCE_NAME="your-acs-resource"
```

## 🔒 **Security Best Practices Implemented**

### **Credential Priority Order**
1. **Environment Variables** (highest priority)
2. **Info.plist** entries
3. **ACSConfig.plist** bundle file
4. **Hardcoded values** (development only)

### **Security Features**
- ✅ Automatic environment detection
- ✅ Mock service fallback for invalid credentials
- ✅ Production validation (no placeholder values allowed)
- ✅ Credential files excluded from version control
- ✅ Secure credential validation
- ✅ Backend token service support

### **Protected Files**
The following files are automatically excluded from git:
- `.env.acs` - Environment variable file
- `**/ACSConfig.plist` - Configuration files with real credentials
- Any files containing real credential data

## 🧪 **Testing Your Configuration**

### **Verify Setup**
```swift
// Check if configuration is valid
let isValid = ACSServiceFactory.validateServiceConfiguration()
print("ACS Configuration Valid: \(isValid)")

// Check which service is being used
let service = ACSServiceFactory.createACSService()
if service is MockACSService {
    print("Using Mock ACS Service")
} else {
    print("Using Real ACS Service")
}
```

### **Health Check**
```swift
Task {
    let isHealthy = await ACSServiceFactory.performHealthCheck()
    print("ACS Service Healthy: \(isHealthy)")
}
```

## 🚀 **Next Steps**

### **For Development**
1. ✅ Replace placeholder credentials in `ACSConfig.plist` OR set environment variables
2. ✅ Build and test the app
3. ✅ Verify real ACS service is being used

### **For Production**
1. ✅ Create production ACS resource in Azure Portal
2. ✅ Implement backend token service
3. ✅ Configure production credentials
4. ✅ Test video calling functionality
5. ✅ Deploy with secure credential management

## 🔗 **Additional Configuration Needed**

### **Azure Portal Setup**
1. Create Azure Communication Services resource
2. Copy connection string, endpoint, and access key
3. Configure CORS settings if using web components
4. Set up identity and access management

### **iOS Project Settings**
- ✅ Camera/microphone permissions already configured in Info.plist
- ✅ Azure Communication Services SDK already integrated
- ✅ Dependency injection already configured

### **Backend Service (Recommended for Production)**
Implement a token service endpoint:
```
POST https://your-backend.com/api/acs-token
Content-Type: application/json

{
  "userId": "user123",
  "displayName": "John Doe"
}

Response:
{
  "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIs...",
  "expiresOn": "2024-01-01T12:00:00Z"
}
```

## 🆘 **Troubleshooting**

### **Common Issues**
1. **"Configuration Invalid" Error** → Check credential format and values
2. **"Using Mock Service" in Production** → Verify real credentials are set
3. **"Token Generation Failed"** → Check network and backend service
4. **"Permission Denied"** → Grant camera/microphone permissions

### **Debug Console Output**
Look for these messages:
- `🔧 Using Mock ACS Service for environment: Development`
- `🚀 Using Real ACS Service for environment: Production`
- `⚠️ Failed to load ACS credentials, falling back to mock service`

## ✅ **Checklist**

Before going to production:
- [ ] Real ACS resource created in Azure Portal
- [ ] Credentials configured (no "your-" placeholders)
- [ ] Backend token service implemented
- [ ] Environment variables or config file updated
- [ ] App builds and runs successfully
- [ ] Video calling tested with real credentials
- [ ] Security review completed
- [ ] Credentials not committed to version control

---

**🎉 Your Azure Communication Services integration is ready!** 

The app will automatically use the appropriate service (mock or real) based on your environment and credential configuration.
