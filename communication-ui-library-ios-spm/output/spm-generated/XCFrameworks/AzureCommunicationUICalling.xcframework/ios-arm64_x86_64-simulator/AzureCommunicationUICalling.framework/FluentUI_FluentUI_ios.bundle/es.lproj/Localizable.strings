//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "En curso";
"Accessibility.ActivityIndicator.Stopped.label" = "El progreso se detuvo";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alerta";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Descartar";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Pulsa dos veces para descartar";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Listo";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Pulse dos veces para alternar la selección";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendario";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Seleccionar una fecha";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d eventos";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "sin eventos";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mes";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Día";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Año";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Fecha";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hora";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuto";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Semana del mes";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Día de la semana";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Primera";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Segunda";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Tercera";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Cuarta";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Última";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Expandir";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Pulse dos veces para expandir";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Contraer";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Pulse dos veces para contraer";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Listo";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Error";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Cargando";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d de %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Perfil de la cuenta";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d de %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Pulsa dos veces para ver más acciones";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Más acciones";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Pulse dos veces para activar la configuración";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Activado";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Desactivado";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elementos";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, no leído";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Más";

/* Generic label for cancel action */
"Common.Cancel" = "Cancelar  ";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Compartido";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Solo yo";

/* Just now date string */
"Date.Now" = "Hace un instante";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Hace %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Hace %ldh";

/* Yesterday string */
"Date.Yesterday" = "Ayer";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Ayer a las %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ a las %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Buscar en el directorio";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Hora de inicio";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Hora de finalización";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Fecha de inicio";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Fecha de finalización";

/* Presence - Available status */
"Presence.Available" = "Disponible";

/* Presence - Away status */
"Presence.Away" = "Ausente";

/* Presence - Busy status */
"Presence.Busy" = "Ocupado";

/* Presence - Do not disturb status */
"Presence.DND" = "No molestar";

/* Presence - Out of office status */
"Presence.OOF" = "Fuera de la oficina";

/* Presence - Offline status */
"Presence.Offline" = "Sin conexión";

/* Presence - Unknown status */
"Presence.Unknown" = "Desconocido";

/* Presence - Blocked status */
"Presence.Blocked" = "Bloqueado";
