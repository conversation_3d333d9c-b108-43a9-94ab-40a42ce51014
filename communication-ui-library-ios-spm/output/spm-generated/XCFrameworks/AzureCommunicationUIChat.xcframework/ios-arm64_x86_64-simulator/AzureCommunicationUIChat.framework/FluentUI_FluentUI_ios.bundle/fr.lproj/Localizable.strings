//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "En cours";
"Accessibility.ActivityIndicator.Stopped.label" = "Progrès stoppés";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alerte";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Ignorer";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Appuyez deux fois pour masquer";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Terminé";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Appuyez deux fois pour activer la sélection";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendrier";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Sélectionner une date";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d événements";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "aucun événement";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mois";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Jour";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Année";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Date";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Heure";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minute";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Semaine du mois";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Jour de la semaine";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Première";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Deuxième";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Troisième";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Quatrième";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Dernière";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Développer";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Appuyez deux fois pour développer";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Réduire";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Appuyez deux fois pour réduire";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Terminé";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Échec";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Chargement";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d sur %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil de compte";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d of %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Appuyez deux fois pour afficher d’autres actions";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Autres actions";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Appuyez deux fois pour activer/désactiver le paramètre";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Activé";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Désactivé";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ éléments";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, non lu(s)";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Plus";

/* Generic label for cancel action */
"Common.Cancel" = "Annuler";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Partagé";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Moi uniquement";

/* Just now date string */
"Date.Now" = "À l’instant";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Il y a %ldm";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Il y a %ldh";

/* Yesterday string */
"Date.Yesterday" = "Hier";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Hier à %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ à %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Rechercher dans l’annuaire";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Heure de début";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Heure de fin";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Date de début";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Date de fin";

/* Presence - Available status */
"Presence.Available" = "Disponible";

/* Presence - Away status */
"Presence.Away" = "Absent(e)";

/* Presence - Busy status */
"Presence.Busy" = "Occupé(e)";

/* Presence - Do not disturb status */
"Presence.DND" = "Ne pas déranger";

/* Presence - Out of office status */
"Presence.OOF" = "Absent(e) du bureau";

/* Presence - Offline status */
"Presence.Offline" = "Hors connexion";

/* Presence - Unknown status */
"Presence.Unknown" = "Inconnu";

/* Presence - Blocked status */
"Presence.Blocked" = "Bloqué";
