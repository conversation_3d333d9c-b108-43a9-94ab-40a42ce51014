//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "W toku";
"Accessibility.ActivityIndicator.Stopped.label" = "Postęp wstrzymany";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alert";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Odrzuć";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Naciśnij dwukrotnie, aby odr<PERSON><PERSON><PERSON>";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Gotowe";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Dotknij dwukrotnie, aby przełączyć zaznaczenie";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Kalendarz";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Wybierz datę";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "Zdarzenia: %d";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "brak zdarzeń";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Miesiąc";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dzień";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Rok";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Data";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Godzina";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuta";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Tydzień miesiąca";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dzień tygodnia";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Pierwszy(a)";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Drugi(a)";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Trzeci(a)";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Czwarty(a)";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Ostatni(a)";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Rozwiń";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Naciśnij dwukrotnie, aby rozwinąć";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Zwiń";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Kliknij dwukrotnie, aby zwinąć";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Gotowe";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Niepowodzenie";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Ładowanie";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d z %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Profil konta";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d z %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Naciśnij dwukrotnie, aby wyświetlić więcej akcji";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Więcej akcji";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Naciśnij dwukrotnie, aby przełączyć ustawienie";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Włączone";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Wyłączone";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, liczba elementów: %@";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, nieprzeczytane";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Więcej";

/* Generic label for cancel action */
"Common.Cancel" = "Anuluj";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Udostępniono";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Tylko ja";

/* Just now date string */
"Date.Now" = "W tej chwili";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm temu";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh temu";

/* Yesterday string */
"Date.Yesterday" = "Wczoraj";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Wczoraj o %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ o %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Wyszukaj w katalogu";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Godzina rozpoczęcia";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Godzina zakończenia";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Data rozpoczęcia";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Data zakończenia";

/* Presence - Available status */
"Presence.Available" = "Dostępne";

/* Presence - Away status */
"Presence.Away" = "Z dala od komputera";

/* Presence - Busy status */
"Presence.Busy" = "Zajęty";

/* Presence - Do not disturb status */
"Presence.DND" = "Nie przeszkadzać";

/* Presence - Out of office status */
"Presence.OOF" = "Poza biurem";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Nieznany";

/* Presence - Blocked status */
"Presence.Blocked" = "Zablokowane";
