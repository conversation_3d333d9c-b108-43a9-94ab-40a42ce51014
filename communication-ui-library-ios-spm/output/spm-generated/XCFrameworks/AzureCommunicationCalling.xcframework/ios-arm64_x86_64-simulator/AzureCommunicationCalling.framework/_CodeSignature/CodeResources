<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>ACSMetalRenderer.metallib</key>
		<data>
		f8feHq815OWUdf5MGUltwogjJFw=
		</data>
		<key>Headers/ACSCallKit.h</key>
		<data>
		CBmikGgNCo5gtju66l7FT5FA1Sw=
		</data>
		<key>Headers/ACSFeatures.h</key>
		<data>
		jaRkVxk3BIgwXQfziRjFfy29H/s=
		</data>
		<key>Headers/ACSStreamSize.h</key>
		<data>
		D9DwOD9+vaSTfi3sgw1BD2sIfIg=
		</data>
		<key>Headers/ACSVideoStreamRenderer.h</key>
		<data>
		r97E81yVwan0gp4RXqp0SqbByjU=
		</data>
		<key>Headers/ACSVideoStreamRendererView.h</key>
		<data>
		QX4k0LZ4ArjTnzI8Jqd+Gkw/5Sc=
		</data>
		<key>Headers/AzureCommunicationCalling-Swift.h</key>
		<data>
		/2Jt04M6EpIEJYkMZyROWmfdBGg=
		</data>
		<key>Headers/AzureCommunicationCalling.h</key>
		<data>
		wph/Iwqq77EccfT9J9YX0mUr1bI=
		</data>
		<key>Info.plist</key>
		<data>
		tiwGqxcSOKuowWO7Cbk9u5MHi34=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<data>
		FRzfPWbJD2WmB+4noFaU8nPtLpE=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<data>
		+PWureK7SiktcADSlndl30G0dCg=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<data>
		FdRgf7OqCXw8LTEJDTGPBu8BcS4=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<data>
		4piqktidTSb1sZhxIXLbd/DyIYw=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<data>
		FdRgf7OqCXw8LTEJDTGPBu8BcS4=
		</data>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<data>
		wEe90QfCBuR/UWL8lLJNdnY2rWs=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		OeiKbCxs0g/VhZ30DZmnJVUU0Ik=
		</data>
		<key>PrivacyInfo.xcprivacy</key>
		<data>
		Cl8yIx4MGN+Ni4+JQGhbWYPz/tQ=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>ACSMetalRenderer.metallib</key>
		<dict>
			<key>hash2</key>
			<data>
			k9fm7isfPA1WkqPhAEH4VlDaAmjJ7tOhgXY57oIi/cA=
			</data>
		</dict>
		<key>Headers/ACSCallKit.h</key>
		<dict>
			<key>hash2</key>
			<data>
			YnqjK1WAcaA/idv+5Ayq4pBTqSLtp9Vv4aK6cB+wYVY=
			</data>
		</dict>
		<key>Headers/ACSFeatures.h</key>
		<dict>
			<key>hash2</key>
			<data>
			XHzbTxEB1rLy93iVxn+rdoTjF3W9JIOGXXV3h7YpxbI=
			</data>
		</dict>
		<key>Headers/ACSStreamSize.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yO8RLdc3eOYM/vUn6xD0L+6g3JedzgOZnF/HEIGBl4c=
			</data>
		</dict>
		<key>Headers/ACSVideoStreamRenderer.h</key>
		<dict>
			<key>hash2</key>
			<data>
			2OBhpniKULwdjPtVzfylW2N/hAx24HfJrc6/+TjaMBI=
			</data>
		</dict>
		<key>Headers/ACSVideoStreamRendererView.h</key>
		<dict>
			<key>hash2</key>
			<data>
			HPXgOrO8AH6977OdmDtf0ITOQ2KUtYydL5lwXUGUYVc=
			</data>
		</dict>
		<key>Headers/AzureCommunicationCalling-Swift.h</key>
		<dict>
			<key>hash2</key>
			<data>
			CBZr8zfhiJjLdd3II60n3wDOGMGDG9znb6HI0hlZv7k=
			</data>
		</dict>
		<key>Headers/AzureCommunicationCalling.h</key>
		<dict>
			<key>hash2</key>
			<data>
			k+pMdqqjLABA2+QTQY0SZtybJSRl6y6WtZqjzcm5K8g=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/Project/x86_64-apple-ios-simulator.swiftsourceinfo</key>
		<dict>
			<key>hash2</key>
			<data>
			nsEKGgbViOfZKZx4Onzn+2qrxfN/z/W6fb+e29BAQiI=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.abi.json</key>
		<dict>
			<key>hash2</key>
			<data>
			fEP6jem+1jxa/MXxq2cZKG7dhE5XCigYn1EUt4KwD4Q=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.private.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			n7qlvFaukJHZlDQNphgsZveyA3sLtmKnJtXluJX+T3k=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftdoc</key>
		<dict>
			<key>hash2</key>
			<data>
			SDbqOilX2M99kO/QcmJsxlaeYirI9RjicRexgHH4YbM=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftinterface</key>
		<dict>
			<key>hash2</key>
			<data>
			n7qlvFaukJHZlDQNphgsZveyA3sLtmKnJtXluJX+T3k=
			</data>
		</dict>
		<key>Modules/AzureCommunicationCalling.swiftmodule/x86_64-apple-ios-simulator.swiftmodule</key>
		<dict>
			<key>hash2</key>
			<data>
			+PCu1VETvuKcVdWriVSCIpqmaO9DHucZ07U6UnciTeo=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			LORdCOLnvKsRfQ4FxDs6bhy1Q9Gw2ifntkGDem/i5U4=
			</data>
		</dict>
		<key>PrivacyInfo.xcprivacy</key>
		<dict>
			<key>hash2</key>
			<data>
			M9Imm1S0XYFDmUbOb5cQj9PwTNj+UOgF681ZBia359Y=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
