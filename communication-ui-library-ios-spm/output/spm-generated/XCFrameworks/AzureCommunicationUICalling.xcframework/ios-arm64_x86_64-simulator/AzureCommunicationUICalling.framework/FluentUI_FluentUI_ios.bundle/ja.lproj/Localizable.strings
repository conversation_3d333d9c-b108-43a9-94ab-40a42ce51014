//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "進行中";
"Accessibility.ActivityIndicator.Stopped.label" = "進行状況が停止しました";

/* Accessibility alert for common use */
"Accessibility.Alert" = "アラート";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "閉じる";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "ダブルタップして閉じます";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "完了";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "ダブルタップして、選択を切り替えます ";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "予定表";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "日付の選択";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d 件のイベント";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "イベントなし";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "月";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "日";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "年";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "日付";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "時間";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "分";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "午前/午後";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "月の週";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "曜日";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "第 1";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "第 2";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "第 3";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "第 4";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "最終";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "展開する";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "ダブルタップして展開";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "折りたたむ";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "ダブルタップして折りたたむ";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "完了";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "失敗";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "読み込み中";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "全 %2$d ページ中 %1$d ページ目";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "アカウント プロファイル";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@、 %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "全 %2d ページ中 %1d ページ目";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "ダブルタップしてその他のアクションを表示します";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "その他のアクション";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "ダブルタップして設定を切り替えます";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "オン";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "オフ";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@、%@ 個の項目";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@、未読";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "その他";

/* Generic label for cancel action */
"Common.Cancel" = "キャンセル";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "共有済み";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "自分のみ";

/* Just now date string */
"Date.Now" = "たった今";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm 前";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh 前";

/* Yesterday string */
"Date.Yesterday" = "昨日";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "昨日 %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ の %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "ディレクトリの検索";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "開始時刻";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "終了時刻";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "開始日";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "終了日";

/* Presence - Available status */
"Presence.Available" = "連絡可能";

/* Presence - Away status */
"Presence.Away" = "退席中";

/* Presence - Busy status */
"Presence.Busy" = "取り込み中";

/* Presence - Do not disturb status */
"Presence.DND" = "応答不可";

/* Presence - Out of office status */
"Presence.OOF" = "外出中";

/* Presence - Offline status */
"Presence.Offline" = "オフライン";

/* Presence - Unknown status */
"Presence.Unknown" = "不明";

/* Presence - Blocked status */
"Presence.Blocked" = "ブロック";
