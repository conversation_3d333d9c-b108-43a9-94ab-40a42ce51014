//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "En curs";
"Accessibility.ActivityIndicator.Stopped.label" = "S'ha aturat el progrés";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Alerta";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Descar<PERSON>";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Toqueu dues vegades per descartar-ho.";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Fet";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Toca dues vegades per commutar la selecció";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendari";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Selecciona una data";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d esdeveniments";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "no hi ha cap esdeveniment";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mes";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Dia";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Any";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Data";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Hora";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minut";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "a.m./p.m.";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Setmana del mes";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Dia de la setmana";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Primer";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Segon";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Tercer";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Quart";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Darrer";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Expandeix";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Toqueu dues vegades per expandir";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Redueix";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Toqueu dues vegades per reduir";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Fet";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Amb errors";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "S'està carregant";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d de %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Perfil del compte";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d de %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Toqueu dues vegades per veure més accions.";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Més accions";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Toca dues vegades per activar o desactivar la configuració.";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Activat";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Desactivat";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elements";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, sense llegir";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Més";

/* Generic label for cancel action */
"Common.Cancel" = "Cancel·la";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Compartit";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Només jo";

/* Just now date string */
"Date.Now" = "Ara mateix";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "Fa %ldm minuts";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "Fa %ldh hores";

/* Yesterday string */
"Date.Yesterday" = "Ahir";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Ahir a les %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "El %@ a les %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Cerca al directori";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Hora d'inici";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Hora de finalització";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Data d'inici";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Data de finalització";

/* Presence - Available status */
"Presence.Available" = "Disponible";

/* Presence - Away status */
"Presence.Away" = "Absent";

/* Presence - Busy status */
"Presence.Busy" = "Ocupat";

/* Presence - Do not disturb status */
"Presence.DND" = "No molesteu";

/* Presence - Out of office status */
"Presence.OOF" = "Fora de l'oficina";

/* Presence - Offline status */
"Presence.Offline" = "Sense connexió";

/* Presence - Unknown status */
"Presence.Unknown" = "Desconegut";

/* Presence - Blocked status */
"Presence.Blocked" = "Bloquejat";
