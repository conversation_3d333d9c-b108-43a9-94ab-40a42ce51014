import Foundation

// MARK: - Dependency Injection Container Protocol
protocol DIContainer {
    func resolve<T>(_ type: T.Type) -> T
    func register<T>(_ type: T.Type, factory: @escaping () -> T)
    func register<T>(_ type: T.Type, singleton: Bool, factory: @escaping () -> T)
}

// MARK: - App DI Container Implementation
class AppDIContainer: DIContainer {
    private var services: [String: Any] = [:]
    private var singletons: [String: Any] = [:]

    func register<T>(_ type: T.Type, factory: @escaping () -> T) {
        register(type, singleton: false, factory: factory)
    }

    func register<T>(_ type: T.Type, singleton: Bool, factory: @escaping () -> T) {
        let key = String(describing: type)
        if singleton {
            services[key] = { [weak self] in
                if let existing = self?.singletons[key] as? T {
                    return existing
                }
                let instance = factory()
                self?.singletons[key] = instance
                return instance
            }
        } else {
            services[key] = factory
        }
    }

    func resolve<T>(_ type: T.Type) -> T {
        let key = String(describing: type)
        guard let factory = services[key] as? () -> T else {
            fatalError("Service \(key) not registered")
        }
        return factory()
    }
}

// MARK: - DI Container Configuration
extension AppDIContainer {
    
    static func configure() -> AppDIContainer {
        let container = AppDIContainer()
        
        // Register Repositories
        container.register(AuthRepositoryProtocol.self) {
            MockAuthRepository()
        }
        
        container.register(UserRepositoryProtocol.self) {
            MockUserRepository()
        }
        
        container.register(NewsRepositoryProtocol.self) {
            MockNewsRepository()
        }
        
        container.register(NotificationRepositoryProtocol.self) {
            MockNotificationRepository()
        }
        
        // Register Infrastructure Services
        container.register(ValidationServiceProtocol.self) {
            ValidationService()
        }

        container.register(NavigationServiceProtocol.self, singleton: true) {
            NavigationService()
        }

        // Register ACS Services using factory pattern
        container.register(ACSTokenProviderProtocol.self) {
            ACSServiceFactory.createTokenProvider()
        }

        container.register(ACSServiceProtocol.self, singleton: true) {
            ACSServiceFactory.createACSService()
        }


        // Register Navigation Coordinator as singleton
        container.register(NavigationCoordinator.self, singleton: true) {
            NavigationCoordinator()
        }
        
        // Register Use Cases
        container.register(AuthUseCaseProtocol.self) {
            AuthUseCase(repository: container.resolve(AuthRepositoryProtocol.self))
        }
        
        container.register(UserUseCaseProtocol.self) {
            UserUseCase(repository: container.resolve(UserRepositoryProtocol.self))
        }
        
        container.register(NewsUseCaseProtocol.self) {
            NewsUseCase(repository: container.resolve(NewsRepositoryProtocol.self))
        }
        
        container.register(NotificationUseCaseProtocol.self) {
            NotificationUseCase(repository: container.resolve(NotificationRepositoryProtocol.self))
        }
        
        // Register Domain Use Cases (Abstractions)
        container.register(ValidationUseCase.self) {
            ValidationUseCaseImpl(validationService: container.resolve(ValidationServiceProtocol.self))
        }
        
        container.register(AuthNavigationUseCase.self) {
            AuthNavigationUseCaseImpl(navigationService: container.resolve(NavigationServiceProtocol.self))
        }
        
        return container
    }
}
