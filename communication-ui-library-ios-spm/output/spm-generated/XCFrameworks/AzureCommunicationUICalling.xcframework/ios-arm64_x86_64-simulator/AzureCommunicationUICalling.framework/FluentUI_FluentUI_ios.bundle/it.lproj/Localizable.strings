//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "In corso";
"Accessibility.ActivityIndicator.Stopped.label" = "Avanzamento interrotto";

/* Accessibility alert for common use */
"Accessibility.Alert" = "Avviso";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "Ignora";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "Effettua un doppio tocco per ignorare l'elemento";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "Fine";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "Effettua un doppio tocco per attivare o disattivare la selezione";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "Calendario";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "Seleziona una data";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d eventi";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "nessun evento";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "Mese";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "Giorno";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "Anno";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "Data";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "Ora";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "Minuto";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "AM/PM";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "Settimana del mese";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "Giorno della settimana";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "Prima";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "Seconda";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "Terza";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "Quarta";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "Ultima";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "Espandi";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "Tocca due volte per espandere";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "Comprimi";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "Tocca due volte per comprimere";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "Fine";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "Non riuscita";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "Caricamento in corso";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d di %2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "Creazione del profilo";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@, %@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "%1d di %2d";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "Effettua un doppio tocco per visualizzare altre azioni";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "Altre azioni";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "Tocca due volte per attivare/disattivare l'impostazione";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "Attivato";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "Disattivato";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@, %@ elementi";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@, da leggere";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "Altro";

/* Generic label for cancel action */
"Common.Cancel" = "Annulla";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "Condivisi";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "Solo io";

/* Just now date string */
"Date.Now" = "Appena aggiornato";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm fa";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh fa";

/* Yesterday string */
"Date.Yesterday" = "Ieri";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "Ieri alle %@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ alle %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "Cerca nella directory";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "Ora di inizio";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "Ora di fine";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "Data di inizio";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "Data di fine";

/* Presence - Available status */
"Presence.Available" = "Disponibile";

/* Presence - Away status */
"Presence.Away" = "Assente";

/* Presence - Busy status */
"Presence.Busy" = "Occupato";

/* Presence - Do not disturb status */
"Presence.DND" = "Non disturbare";

/* Presence - Out of office status */
"Presence.OOF" = "Fuori sede";

/* Presence - Offline status */
"Presence.Offline" = "Offline";

/* Presence - Unknown status */
"Presence.Unknown" = "Sconosciuto";

/* Presence - Blocked status */
"Presence.Blocked" = "Bloccato";
