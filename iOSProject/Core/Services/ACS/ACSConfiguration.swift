//
//  ACSConfiguration.swift
//  iOSProject
//
//  Created by Azure Communication Services Integration on 11/08/2025.
//

import Foundation

// MARK: - ACS Configuration

/// Configuration model for Azure Communication Services
struct ACSConfiguration {
    let connectionString: String
    let defaultDisplayName: String
    let callTimeout: TimeInterval
    let credentials: ACSCredentials?

    /// Initialize with credentials
    init(connectionString: String, defaultDisplayName: String, callTimeout: TimeInterval, credentials: ACSCredentials? = nil) {
        self.connectionString = connectionString
        self.defaultDisplayName = defaultDisplayName
        self.callTimeout = callTimeout
        self.credentials = credentials
    }

    /// Default configuration with automatic credential detection
    static let `default`: ACSConfiguration = {
        do {
            let credentials = try ACSCredentials()
            return ACSConfiguration(
                connectionString: credentials.connectionString,
                defaultDisplayName: AppConstants.ACS.defaultDisplayName,
                callTimeout: AppConstants.ACS.callTimeout,
                credentials: credentials
            )
        } catch {
            // Fallback to placeholder values for development
            return ACSConfiguration(
                connectionString: "endpoint=https://your-acs-resource.communication.azure.com/;accesskey=your-access-key",
                defaultDisplayName: AppConstants.ACS.defaultDisplayName,
                callTimeout: AppConstants.ACS.callTimeout,
                credentials: nil
            )
        }
    }()

    /// Check if configuration is using real credentials
    var isUsingRealCredentials: Bool {
        return credentials?.isUsingMockCredentials == false
    }

    /// Get environment
    var environment: ACSCredentials.Environment {
        return credentials?.environment ?? .development
    }

    /// Validates that credentials are properly configured and not using placeholder/mock values
    func validateCredentials() -> ACSCredentialValidationResult {
        // Check if credentials exist
        guard let credentials = credentials else {
            return .invalid(.missingCredentials)
        }

        // Check if using real credentials (not mock/development)
        guard isUsingRealCredentials else {
            return .invalid(.usingMockCredentials)
        }

        // Check for placeholder values in connection string
        let placeholderPatterns = [
            "your-acs-resource",
            "your-access-key",
            "endpoint=https://your-acs-resource.communication.azure.com/",
            "accesskey=your-access-key"
        ]

        for pattern in placeholderPatterns {
            if connectionString.contains(pattern) {
                return .invalid(.placeholderCredentials)
            }
        }

        // Basic connection string format validation
        guard connectionString.contains("endpoint=") && connectionString.contains("accesskey=") else {
            return .invalid(.invalidConnectionString)
        }

        // Ensure connection string is not empty or just whitespace
        guard !connectionString.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return .invalid(.invalidConnectionString)
        }

        return .valid
    }
}

// MARK: - ACS Call Configuration

/// Configuration for different types of calls
enum ACSCallConfiguration {
    case groupCall(groupId: String, displayName: String?)
    case teamsMeeting(url: String, displayName: String?)
    case oneToOneCall(userId: String, displayName: String?)
    
    var displayName: String {
        switch self {
        case .groupCall(_, let name), .teamsMeeting(_, let name), .oneToOneCall(_, let name):
            return name ?? ACSConfiguration.default.defaultDisplayName
        }
    }
}

// MARK: - ACS Credential Validation

/// Result of credential validation
enum ACSCredentialValidationResult: Equatable {
    case valid
    case invalid(ACSCredentialValidationError)
}

/// Specific errors that can occur during credential validation
enum ACSCredentialValidationError: Equatable {
    case usingMockCredentials
    case placeholderCredentials
    case invalidConnectionString
    case missingCredentials
}

// MARK: - ACS Error Types

/// Comprehensive error handling for ACS operations
enum ACSError: LocalizedError {
    case configurationInvalid
    case tokenExpired
    case tokenGenerationFailed
    case permissionDenied(String)
    case networkError
    case callFailed(String)
    case compositeInitializationFailed
    case invalidCallConfiguration
    
    var errorDescription: String? {
        switch self {
        case .configurationInvalid:
            return "Azure Communication Services configuration is invalid"
        case .tokenExpired:
            return "Communication token has expired. Please refresh and try again."
        case .tokenGenerationFailed:
            return "Failed to generate communication token"
        case .permissionDenied(let permission):
            return "\(permission) permission is required for communication features"
        case .networkError:
            return "Network connection is required for communication services"
        case .callFailed(let message):
            return "Call failed: \(message)"
        case .compositeInitializationFailed:
            return "Failed to initialize communication interface"
        case .invalidCallConfiguration:
            return "Invalid call configuration provided"
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .configurationInvalid:
            return "Please check your Azure Communication Services configuration"
        case .tokenExpired, .tokenGenerationFailed:
            return "Please refresh the application and try again"
        case .permissionDenied:
            return "Please grant the required permissions in Settings"
        case .networkError:
            return "Please check your internet connection"
        case .callFailed:
            return "Please try again or contact support if the issue persists"
        case .compositeInitializationFailed:
            return "Please restart the application and try again"
        case .invalidCallConfiguration:
            return "Please check the call parameters and try again"
        }
    }
}

// MARK: - ACS Call State

/// Represents the current state of an ACS call
enum ACSCallState {
    case idle
    case connecting
    case connected
    case disconnecting
    case disconnected
    case failed(ACSError)
}
