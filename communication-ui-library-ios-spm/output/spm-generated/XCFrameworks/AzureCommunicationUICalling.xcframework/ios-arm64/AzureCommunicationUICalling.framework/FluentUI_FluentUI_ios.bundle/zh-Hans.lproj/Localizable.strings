//
//  Copyright (c) Microsoft Corporation. All rights reserved.
//  Licensed under the MIT License.
//

// Accessibility
"Accessibility.ActivityIndicator.Animating.label" = "正在进行";
"Accessibility.ActivityIndicator.Stopped.label" = "已暂停进度";

/* Accessibility alert for common use */
"Accessibility.Alert" = "警报";
/* Accessibility dismiss label for common use */
"Accessibility.Dismiss.Label" = "关闭";
/* Accessibility dismiss hint for common use */
"Accessibility.Dismiss.Hint" = "双击以关闭";
/* Accessibility done label for common use */
"Accessibility.Done.Label" = "完成";
/* Accessibility multi select hint for common use */
"Accessibility.MultiSelect.Hint" = "双击以切换所选内容";

/* Accessibility label for the upper calendar date picker view. */
"Accessibility.Calendar.Label" = "日历";

/* Accessibility hint for the upper calendar date picker view */
"Accessibility.Calendar.Hint" = "选择日期";

// TODO: Change to a stringsdict format
// TODO: Reassess for generic indicator
/* Accessibility: Number of events on the given day (%d is the number) */
"Accessibility.Calendar.Events.Number" = "%d 个事件";

/* Accessibility: There's no events on the given day */
"Accessibility.Calendar.Events.None" = "无事件";

/* Accessibility label for month in date time picker */
"Accessibility.DateTime.Month.Label" = "月";

/* Accessibility label for day in date time picker */
"Accessibility.DateTime.Day.Label" = "日";

/* Accessibility label for year in date time picker */
"Accessibility.DateTime.Year.Label" = "年";

/* Accessibility label for date in date time picker */
"Accessibility.DateTime.Date.Label" = "日期";

/* Accessibility label for hour in date time picker */
"Accessibility.DateTime.Hour.Label" = "小时";

/* Accessibility label for minute in date time picker */
"Accessibility.DateTime.Minute.Label" = "分钟";

/* Accessibility label for AM/PM in date time picker */
"Accessibility.DateTime.AMPM.Label" = "上午/下午";

/* Accessibility label for week of month in date time picker */
"Accessibility.DateTime.WeekOfMonth.Label" = "月的第几周";

/* Accessibility label for day of week in date time picker */
"Accessibility.DateTime.DayOfWeek.Label" = "星期几";

/* Accessibility label for first week of the month */
"Accessibility.DateTime.Week.First" = "第一个";

/* Accessibility label for second week of the month */
"Accessibility.DateTime.Week.Second" = "秒";

/* Accessibility label for third week of the month */
"Accessibility.DateTime.Week.Third" = "第三个";

/* Accessibility label for fourth week of the month */
"Accessibility.DateTime.Week.Fourth" = "第四个";

/* Accessibility label for last week of the month */
"Accessibility.DateTime.Week.Last" = "最后一个";

/* Accessibility label for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Label.Expand" = "展开";
/* Accessibility hint for drawer's resizing handle when it has expand action */
"Accessibility.Drawer.ResizingHandle.Hint.Expand" = "双击以展开";
/* Accessibility label for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Label.Collapse" = "折叠";
/* Accessibility hint for drawer's resizing handle when it has collapse action */
"Accessibility.Drawer.ResizingHandle.Hint.Collapse" = "双击以折叠";

/* Accessibility label for when a task under progress has finished */
"Accessibility.HUD.Done" = "完成";

/* Accessibility label for when a task under progress has failed */
"Accessibility.HUD.Failed" = "失败";

/* Accessibility label for when a task is in progress */
"Accessibility.HUD.Loading" = "正在加载";

/* Accessibility hint for MSPillButtons in an MSPillButtonBar. %1$d is placeholder for index and %2$d is a placeholder for total number of items */
"Accessibility.MSPillButtonBar.Hint" = "%1$d/%2$d";

/* Accessibility label for the avatar (or profile) view */
"Accessibility.LargeTitle.ProfileView" = "帐户个人资料";

/* Accessibility label format string for avatar view. Format: "<Name>, <Presence>". Example: "Kat, Available" */
"Accessibility.AvatarView.LabelFormat" = "%@、%@";

/* Accessibility hint for TabBarItemView in TabBarView. %1$d is placeholder for index and %2$d is a placeholder for total number of tab items  */
"Accessibility.TabBarItemView.Hint" = "第 %1d 个，共 %2d 个";

/* Accessibility hint for more actions button */
"Accessibility.TableViewCell.MoreActions.Hint" = "双击以查看更多操作";

/* Accessibility label for more actions button */
"Accessibility.TableViewCell.MoreActions.Label" = "更多操作";

/* Accessibility hint for cell when accessory view is a UISwitch */
"Accessibility.TableViewCell.Switch.Hint" = "双击以切换设置";

/* Accessibility value for when a UISwitch is on */
"Accessibility.TableViewCell.Switch.On" = "开";

/* Accessibility value for when a UISwitch is off */
"Accessibility.TableViewCell.Switch.Off" = "关";

/* Format string for tab bar item accessbility labels. Format: "<Title>, <BadgeValue> items". Example: "Home, 5 items" */
"Accessibility.TabBarItemView.LabelFormat" = "%@、%@ 个项目";

/* Accessibility hint for TabBarItem in TabBarItemView. Indicates whether the item is unread or not. Format: "<Title>, unread". Example: "Files, unread" */
"Accessibility.TabBarItemView.UnreadFormat" = "%@，未读";

/* Format string for badge label button accessibility label. Format: "<Item Accessibility>, <Badge Label Accessibility>". Example: "Notifications, 5 new notifications" */
"Accessibility.BadgeLabelButton.LabelFormat" = "%@, %@";

/* Commanding Bottom Bar - More button */
"CommandingBottomBar.More" = "更多";

/* Generic label for cancel action */
"Common.Cancel" = "取消";

/* Generic label to indicate that an item is shared */
"Common.Shared" = "已共享";

/* Generic label to indicate that an item is not shared */
"Common.OnlyMe" = "只有我";

/* Just now date string */
"Date.Now" = "刚刚";

/* Minutes ago date format string. Example: "5m ago" */
"Date.FormatMinutes" = "%ldm 前";

/* Hours ago date format string. Example: "5h ago" */
"Date.FormatHours" = "%ldh 前";

/* Yesterday string */
"Date.Yesterday" = "昨天";

/* Yesterday and time format string. Example: "Yesterday at 11:00 AM" */
"Date.FormatYesterdayTime" = "昨天%@";

/* Day and time format string. Example: "Fri at 11:00 AM" */
"Date.FormatDayTime" = "%@ %@";

/* MSPersonaListView cell title: search(action) the directory */
"MSPersonaListView.SearchDirectory" = "搜索目录";

/* MSDateTimePicker label for the start time of a date range */
"MSDateTimePicker.StartTime" = "开始时间";

/* MSDateTimePicker label for the end time of a date range */
"MSDateTimePicker.EndTime" = "结束时间";

/* MSDateTimePicker label for the start date of a date range */
"MSDateTimePicker.StartDate" = "开始日期";

/* MSDateTimePicker label for the end date of a date range */
"MSDateTimePicker.EndDate" = "结束日期";

/* Presence - Available status */
"Presence.Available" = "可用";

/* Presence - Away status */
"Presence.Away" = "离开";

/* Presence - Busy status */
"Presence.Busy" = "忙碌";

/* Presence - Do not disturb status */
"Presence.DND" = "勿扰";

/* Presence - Out of office status */
"Presence.OOF" = "外出";

/* Presence - Offline status */
"Presence.Offline" = "脱机";

/* Presence - Unknown status */
"Presence.Unknown" = "未知";

/* Presence - Blocked status */
"Presence.Blocked" = "已阻止";
